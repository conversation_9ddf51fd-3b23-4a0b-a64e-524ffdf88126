{"name": "d<PERSON><PERSON>", "author": "Dark Cloud", "version": "9.10.53", "main": "./main.js", "build": {"productName": "DSAdmin", "appId": "d<PERSON><PERSON>", "win": {"target": ["portable"]}, "portable": {"artifactName": "DSAdmin.exe"}, "directories": {"output": "electron/output", "app": "electron/app", "buildResources": "electron/buildResources"}}, "scripts": {"ng": "ng", "build": "ng build --prod", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "electron": "electron .", "electron-build": "ng build --configuration=production && electron .", "electron-package": "attrib -r /d /s && attrib -r /d /s C:/Users/<USER>/AppData/Local/Temp/electron-packager/win32-x64/dsadmin-win32-x64/resources/app && attrib -r /d /s C:/Users/<USER>/AppData/Local/Temp/electron-packager/win32-x64/mapsys-win32-x64/resources/app && electron-packager . --platform=win32 --overwrite", "electron-package2": "electron-packager . --platform=win32 --overwrite", "doc": "compodoc -p tsconfig.doc.json -s", "client": "ng serve --proxy-config server.conf.json", "server": "nodemon server.js", "start": "npm-run-all -p client server"}, "private": true, "dependencies": {"@angular/animations": "^15.0.0", "@angular/common": "^15.0.0", "@angular/compiler": "^15.0.0", "@angular/core": "^15.0.0", "@angular/forms": "^15.0.0", "@angular/platform-browser": "^15.0.0", "@angular/platform-browser-dynamic": "^15.0.0", "@angular/router": "^15.0.0", "@dqbd/tiktoken": "^1.0.20", "@ng-bootstrap/ng-bootstrap": "^19.0.0", "@w11k/angular-sticky-things": "^1.7.0", "body-parser": "^1.20.1", "bootstrap": "3.4.1", "bootstrap-notify": "^3.1.3", "cors": "^2.8.5", "diff": "^7.0.0", "express": "^4.18.2", "idb": "^6.1.1", "jquery": "^3.5.1", "lodash": "^4.17.21", "mime-types": "^2.1.35", "multer": "^1.4.5-lts.1", "nodemon": "^2.0.20", "openai": "^4.91.1", "rxjs": "~7.5.0", "sqlite3": "^5.1.2", "sweetalert2": "^9.17.1", "tslib": "^2.3.0", "xlsx": "^0.17.1", "zone.js": "~0.12.0"}, "devDependencies": {"@angular-devkit/build-angular": "^15.0.0", "@angular/cli": "~15.0.0", "@angular/compiler-cli": "^15.0.0", "@types/jasmine": "~4.3.0", "@types/jasminewd2": "~2.0.3", "@types/node": "^12.11.1", "electron": "^21.3.1", "electron-packager": "^13.1.1", "jasmine-core": "~4.5.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "protractor": "~7.0.0", "ts-node": "~8.3.0", "typescript": "~4.8.2"}}