<div class="background-div" (mousedown)="handleOutsideMouseClick($event)">
  <link rel="preload" href="assets/img/icon_like.png" as="image" />
  <link rel="preload" href="assets/img/icon_deslike.png" as="image" />
  <link rel="preload" href="assets/svg/dice-d20.svg" as="image" />

  <!--MODAL-->
  <ng-container *ngIf="popupStats">
    <div class="modal-backdrop" *ngIf="popupStats"></div>
    <div id="modal-close" @popup class="popup-report" [ngStyle]="{ width: openITDBox ? '1360px' : '750PX' }"
      style="background-color: transparent">
      <div class="total-modal">
        <div class="c-title">
          <div class="modal-close btn-close">
            <button (click)="closeAreaStatsPopup()" class="btn btn-danger btn-fill btn-remove modal-btn-close">
              <i class="pe-7s-close i-icon"></i>
            </button>
          </div>
        </div>

        <div class="comp-container">
          <div class="width-container">
            <div class="box" id="boxOpponent">
                <div class="title-element">
                 <h4>Opponent</h4>
               </div>             
              <ul id="opponentList">
                <li *ngFor="let speak of preloadedSpeakers"
                  [ngStyle]="{'background-color': selectedOpponent === speak ? '#e6e6e6' : ''}"
                  style="text-align: start; cursor: pointer" (click)="getOpponent(speak)">
                  {{ speak.name }}
                </li>
              </ul>
            </div>
            <div style="text-align: start">
              <div style="display: ruby">
                Boss Level (BL):
                <p style="font-weight: 700">
                  {{
                  valueBL || currentBL?.bl || (selectedOpponent ? 0 : "")
                  }}
                </p>
              </div>
              <br />
              <div style="display: ruby">
                Class:
                <p style="font-weight: 700">{{ classeName }}</p>
              </div>
            </div>
            <ng-container *ngIf="isReset">
              <button class="btn confirm btn-danger btn-reset" id="reset" (click)="resetDice(optionReset)">
                Reset
              </button>
            </ng-container>
          </div>

          <ng-container *ngIf="openAttributeBox">
            <div class="width-container">
              <div class="box" id="boxAttribute">
                <div class="title-element">
                   <h4 >Attribute</h4>
                </div>                
                <ul id="attributeList">
                  <li *ngFor="let atri of atributte" style="text-align: start" [ngStyle]="{
                    'background-color': selectedAttribute === atri ? '#e6e6e6' : ''}" (click)="getAttribute(atri)">
                    {{ atri.atributte }}
                  </li>
                </ul>
              </div>
              <div class="c-modifier">
                Class modifier (CM):
                <p style="font-weight: 700">{{ mc }}</p>
              </div>
              
              <ng-container *ngIf="listMoonAttributes.length > 0 || mc">
              <div class="toggle-switch">
                  ModMoon: 
                  <i id="explain" style="font-size: 20px !important;" placement='top' delay='250' ttWidth="450px" ttAlign="center"
                  ttPadding="10px" tooltip="Applies the moon’s effect to this test, adjusting the Difficulty Class (DC) according to the current moon phase in the game." class="pe-7s-help1"></i>
                    <input type="checkbox" id="toggleSwitch" [(ngModel)]="isModMonn" (change)="onToggleChange($event)">
                    <label for="toggleSwitch" class="switch-label">
                      <span class="switch-text">{{ isModMonn ? 'ON' : 'OFF' }}</span>
                      <span class="switch-slider"></span>
                    </label>
                </div>
              </ng-container>
 
              <div style="margin-bottom: 0 !important; font-size: 12px;" *ngFor="let moonAttribute of listMoonAttributes; let i = index">       
                 {{i + 1 + ') '}} {{ moonAttribute }}                 
              </div>
            </div>
          </ng-container>

          <ng-container *ngIf="openSubcontext">
            <div class="width-container">
              <div class="box" id="boxITD">
                <div class="title-element">
                  <h4>Subcontext</h4>
                </div>
                <ul id="itdList">
                  <ng-container *ngFor="let list of listSubcontext">
                    <li *ngFor="let subcontext of list.subContext; let i = index" style="text-align: start" [ngStyle]="{
                        'background-color':
                          selectSubcontext === subcontext ? '#e6e6e6' : ''
                      }" (click)="getTaskSubContext(subcontext, i)">
                      {{ subcontext }}
                    </li>
                  </ng-container>
                </ul>
              </div>
              <ng-container *ngIf="descriptionSubcontext">
                <div style="margin-top: 10px">
                  Description:
                  <p style="font-weight: 700">{{ descriptionSubcontext }}</p>
                </div>
              </ng-container>
            </div>
          </ng-container>

          <ng-container *ngIf="openITDBox">
            <div class="width-container">
              <div class="box" id="boxITD">
                <div class="title-element">
                  <h4>ITD</h4>
                  <p>Intrinsic Task Difficulty</p>
                </div>
                <ul id="itdList">
                  <li *ngFor="let dif of listDit" style="text-align: start"
                    [ngStyle]="{'background-color': selectedITD === dif ? '#e6e6e6' : ''}"
                    (click)="getTaskDifficultyClass(dif)">
                    {{ dif.name }}
                  </li>
                </ul>
              </div>
            </div>
          </ng-container>

          <ng-container *ngIf="openDCBox">
            <div style="padding-right: 20px;">
              <div class="box" id="boxDC" style="width: 180px">
                <div class="title-element">
                  <h4>DC</h4>
                  <p>Difficulty Class</p>
                </div>
                <h1 id="dcValue">{{ resultDC }}</h1>
              </div>

              <ng-container *ngIf="isConfirm">
                <button class="btn confirm btn-primary btn-difficultyClass"
                  [ngStyle]="{width: selectedAttribute ? '180px' : ''}" id="confirmBtn"
                  (click)="confirmDifficultyClass()">
                  Confirm
                </button>
              </ng-container>

              <ng-container *ngIf="descriptionDCGuide">
                <div style="margin-top: 10px">
                  Description:
                  <p style="font-weight: 700">{{ descriptionDCGuide }}</p>
                </div>
              </ng-container>
            </div>
          </ng-container>
        </div>
      </div>
    </div>


  </ng-container>
  <!--FIM DO MODAL-->

  <div class="row" *ngIf="optionBox">
    <div class="col-md-12">
      <div class="card card_choice" id="{{ optionBox.id }}">
        <div *ngFor="let roadblock of this.roadBlocksUseds">
          <div id="{{ roadblock.id }}">
            <app-roadblock [optionBox]="optionBox" [toRemove]="toRemoveProcessConditionFunc" [storyBoxId]="optionBox.id"
              [currentRoadblock]="roadblock" class="center">
            </app-roadblock>
          </div>
        </div>
        <div *ngIf="this.roadBlocksUseds.length > 1" class="extrut-card">
          <div class="cond-card" [ngStyle]="{
              opacity: optionBox.AndOrCondition == 'AND' ? 1 : 0.3,
              color: optionBox.AndOrCondition == 'AND' ? '#ff00aa' : 'black'
            }">
            AND
          </div>
          <label class="switch">
            <input [checked]="optionBox.AndOrCondition == 'AND' ? false : true"
              (change)="this.chooseAndOrCondition($event)" type="checkbox" />
            <span class="slider"></span>
          </label>
          <div class="cond-card" [ngStyle]="{
              opacity: optionBox.AndOrCondition == 'OR' ? 1 : 0.3,
              color: optionBox.AndOrCondition == 'OR' ? 'blue' : 'black'
            }">
            OR
          </div>
        </div>
        <!--Box header-->
        <div class="header">
          <!--Delete button for box-->
          <button id="delete-button" class="btn btn-fill btn-danger pull-right" (click)="toRemove(optionBox)">
            <i class="pe-7s-close-circle"></i>
          </button>

          <!--Box sort order buttons-->
          <div [title]="optionBox.id" class="select pull-right">
            <button class="btn btn-success btn-simple btn-invert btn-xs" (click)="toMove(-1)">
              <i class="pe-7s-angle-up-circle"></i>
            </button>
            {{ index }}
            <button class="btn btn-danger btn-simple btn-invert btn-xs" (click)="toMove(1)">
              <i class="pe-7s-angle-down-circle"></i>
            </button>
          </div>

          <div class="row-responsive">
            <!--Dialog box ID name-->
            <p class="category">
              {{ optionBox | typeName }} > {{ optionBox.id }}
            </p>
            <p class="box-title-watermark">
              {{ optionBox | typeName }}
            </p>
          </div>
          <!--Box title-->
          <div class="header">
            <div class="sub-header">
              <div class="sub-header">
                <i class="icon title {{ optionBox | typeIcon }}"></i>
                <!-- Circle over key -->
                <input class="form-control form-short form-title" type="text" value="{{!optionBox.label ? '<<Label for progress condition>>' : optionBox.label}}" #label (change)="changeLabel($event)" />
              </div>

              <h1 *ngIf="optionBox.label">
                <i [ngStyle]="{ 'color': hasLabel ? '#00ff04' : '#000' }" class="pe-7s-key pe-5x pe-va class-hasLabel">
                  <div id="text-size" *ngIf="this.usedRoadBlocks.length > 0" class="circle branch-circle margIcon"
                    tooltip="Used on: {{ this.usedOnLevels | enumerateList }}" placement="top" delay="300"
                    ttPadding="10px" ttPadding="10px" ttWidth="250px">
                    <p style="text-align: center">
                      {{ this.usedRoadBlocks.length }}
                    </p>
                  </div>
                </i>
              </h1>
            </div>
          </div>
        </div>

        <!-- Developer Note -->
        <div class="developer-note">
          <div class="note-header">
            <i class="pe-7s-info note-icon"></i>
            <strong>Choice Box - Lembrete</strong>
          </div>
          <div class="note-content">
            <p><strong>Interações interpessoais</strong> (Carisma, Diplomacia, Negociação, Intimidação):</p>
            <p><strong>Escolha única:</strong> uma opção exclui as demais e define o rumo.</p>
          </div>
        </div>

        <div class="table-responsive">
          <table class="table table-hover table-box">
            <thead>
              <tr style="text-align-last: center">
                <th>Order</th>
                <th>Thought</th>
                <th>Dice</th>
                <th class="td-100">Message</th>
                <th>Weight</th>
                <th>Answer</th>
                <th>Action</th>
              </tr>
            </thead>
            <!--choice box-->
            <tbody>
              <ng-container *ngIf="optionBoxes">
                <ng-container *ngFor="let option of this.optionBoxes; let i = index">
                  <tr id="{{ option?.id }}">
                    <!--toggle emit-->
                    <td class="td-sort">
                      <div class="compact">
                        <!-- order buttons -->
                        <button class="btn btn-success btn-simple btn-invert btn-xs" (click)="toMoveOption(option, -1)">
                          <i class="pe-7s-angle-up-circle"></i>
                        </button>
                        {{ i }}
                        <button class="btn btn-danger btn-simple btn-invert btn-xs" (click)="toMoveOption(option, 1)">
                          <i class="pe-7s-angle-down-circle"></i>
                        </button>
                      </div>
                    </td>
                    <td style="text-align-last: center">
                      <!--toggle emit-->
                      <input type="checkbox" class="toggle center" [(ngModel)]="option.isOmitted"
                        (change)="onChangeOption(option)" />
                    </td>
                    <!--Difficulty class-->
                    <td>
                      <div class="btn btn-danger btn-simple btn-invert btn-xs" [ngStyle]="{
                          'background-color':
                            option?.choiceDifficulty != undefined  ? '#ff4a55' : ''}" title="Difficulty class"
                        (click)="openModalDificultyClass(option)">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" class="colorSvg">
                          <path
                            d="M242.9 22.5L58 133.5l83.6 62.7L242.9 22.5zM48 146l0 213.9 85.6-149.7L48 146zM68.4 384.8L248 492.5l0-100.8L68.4 384.8zM264 492.5L443.6 384.8 264 391.7l0 100.8zM464 359.9L464 146l-85.5 64.2L464 359.9zM454 133.5l-185-111L370.4 196.2 454 133.5zM243.7 3.4c7.6-4.6 17.1-4.6 24.7 0l200 120c7.2 4.3 11.7 12.1 11.7 20.6l0 224c0 8.4-4.4 16.2-11.7 20.6l-200 120c-7.6 4.6-17.1 4.6-24.7 0l-200-120C36.4 384.2 32 376.4 32 368l0-224c0-8.4 4.4-16.2 11.7-20.6l200-120zm-2.6 372L144.4 223.5l-82.9 145 179.6 6.9zM353.4 216l-194.9 0L256 369.1 353.4 216zM256 31.9L157.9 200l196.1 0L256 31.9zm14.9 343.5l179.6-6.9-82.9-145L270.9 375.4z" />
                        </svg>
                      </div>
                    </td>

                    <!--message-->
                    <td class="td-100">
                      <div>
                        <h6 class="c-message">
                          {{ option?.id }}
                        </h6>
                        <app-rpg-content-input [rpgSuggestionLink]="option" #rpgInputBox [content]="option?.message"
                          (contentChange)="
                            onChangeOptionValue(option, 'message', $event)
                          " [language]="language">
                        </app-rpg-content-input>
                      </div>
                    </td>
                    <!--weight button-->
                    <td>
                      <button class="btn btn-lg btn-fill btn-neutral" (click)="selectWeight(option)"
                        (change)="selectWeight(option)">
                        {{ option?.weight }}
                      </button>
                    </td>
                    <!--Answer-->
                    <td>
                      <button class="btn btn-lg btn-fill" (click)="HighlightElement(option?.answerBoxId, 10, true)">
                        Answer
                      </button>
                    </td>
                    <td clas="td-actions">
                      <button class="btn btn-fill btn-danger btn-remove" (click)="RemoveOption(option)">
                        <i class="pe-7s-close style-icon"></i>
                      </button>
                    </td>
                  </tr>
                </ng-container>
              </ng-container>

            </tbody>
          </table>

          <ng-container *ngIf="+optionBox.type === OptionBoxType.INVESTIGATION">
            <hr>
            <div class="inv-box">
              <p>End Investigation</p>
              <div class="inv-input">
                <p class="form-control form-100 p_endInvest">
                  {{ "DIALOGUE_OPTION_EXIT" | keyword }}
                </p>
              </div>
              <div>
                <i class="pe-7s-next-2 style-icon"></i>
              </div>
            </div>
          </ng-container>


          <div class="row">
            <div class="pull-right">
              <button id="add-message-button" class="btn btn-sm btn-success btn-fill" style="margin-right: 25px"
                (click)="toAddRoadblock()">
                <i class="pe-7s-shield center"></i>
                Add Progress Condition
              </button>
              <button id="add-marker-button" class="btn btn-sm btn-success btn-fill" (click)="toPromptAddOption()">
                <i class="pe-7s-help1 center"></i> Add Choice
              </button>
            </div>
            <div class="row"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <ng-container *ngFor="let option of optionBox?.optionIds | options; let i = index">
    <div class="row">
      <div class="col-md-12">
        <div class="row">
          <ng-container *ngIf="
              option.choiceDifficulty === undefined &&
              option.investigaDifficulty === undefined
            ">
            <div class="col-md-1">
              <button (click)="HighlightElement(option.id, 10, true)" class="btn btn-fill btn-simple card"
                [ngStyle]="optionBox | boxStyle">
                <div class="content table-responsive table-full-width">
                  <i class="icon {{ optionBox | typeIcon }}"></i>
                </div>
              </button>
            </div>
          </ng-container>
          <ng-container *ngIf="
              option.choiceDifficulty !== undefined ||
              option.investigaDifficulty !== undefined">
            <div class="col-md-1">
              <div>
                <img src="assets/img/icon_like.png" style="width: 100px" loading="lazy" />
              </div>
            </div>
          </ng-container>

          <div class="col-md-11">
            <app-answer-box *ngIf="option.answerBoxId != undefined"
              (updateOption)="updateOptionFromAnswerBox($event)" [option]="option.id | option"
              [answerBox]="option.answerBoxId | storyBox" [preloadedSpeakers]="preloadedSpeakers"
              [preloadedMissionsOfArea]="preloadedMissionsOfArea" [optionBox]="optionBox" [language]="language">
            </app-answer-box>
          </div>

          <ng-container *ngIf="
              option.choiceNegative !== undefined ||
              option.investigaDifficulty !== undefined">
            <div class="col-md-1">
              <div>
                <img src="assets/img/icon_deslike.png" style="width: 100px" loading="lazy" />
              </div>
            </div>

            <div class="col-md-11">
              <ng-container *ngIf="option?.answerBoxNegativeId != undefined">
                <app-answer-box (updateOption)="updateOptionFromAnswerBox($event)" [option]="option.id | option"
                  [answerBox]="option.answerBoxNegativeId | storyBox" [preloadedSpeakers]="preloadedSpeakers"
                  [preloadedMissionsOfArea]="preloadedMissionsOfArea" [optionBox]="optionBox" [language]="language">
                </app-answer-box>
              </ng-container>
            </div>            
          </ng-container>
        </div>
      </div>
    </div>
  </ng-container>
</div>