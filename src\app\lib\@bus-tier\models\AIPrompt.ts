import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class AIPrompt extends Base<Data.Hard.IAIPrompt, Data.Result.IAIPrompt> implements Required<Data.Hard.IAIPrompt>
{
  constructor(index: number, dataAccess?: AIPrompt['TDataAccess']) 
  {
    super(
        {
          hard: 
          {
            id: AIPrompt.generateId(index),
          },
        },
        dataAccess
    );
  }

  private static generateId(index: number): string 
  {
    return IdPrefixes.AI_PROMPT + index;
  }

  public get prompt(): string 
  {
    return this.hard.prompt;
  }
  public set prompt(value: string) 
  {
    this.hard.prompt = value;
  }
  public get notes(): string 
  {
    return this.hard.notes;
  }
  public set notes(value: string) 
  {
    this.hard.notes = value;
  }  
  public get description(): string 
  {
    return this.hard.description;
  }
  public set description(value: string) 
  {
    this.hard.description = value;
  }  
  public get selectType(): string 
  {
    return this.hard.selectType;
  }
  public set selectType(value: string) 
  {
    this.hard.selectType = value;
  }
    public get nameEnvironmentAi(): string 
  {
    return this.hard.nameEnvironmentAi;
  }
  public set nameEnvironmentAi(value: string) 
  {
    this.hard.nameEnvironmentAi = value;
  }
  public get idEnvironmentAi(): string 
  {
    return this.hard.idEnvironmentAi;
  }
  public set idEnvironmentAi(value: string) 
  {
    this.hard.idEnvironmentAi = value;
  } 
  public get promptName(): string 
  {
    return this.hard.promptName;
  }
  public set promptName(value: string) 
  {
    this.hard.promptName = value;
  }  
}
