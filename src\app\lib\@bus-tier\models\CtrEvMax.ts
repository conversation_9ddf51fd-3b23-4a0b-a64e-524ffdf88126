import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class CtrEvMax extends Base<Data.Hard.ICtrEvMax, Data.Result.ICtrEvMax> implements Required<Data.Hard.ICtrEvMax>
{
  public static generateId(index: number): string {
    return IdPrefixes.CTREVMAX + index;
  }

  constructor( index: number, dataAccess: CtrEvMax['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: CtrEvMax.generateId(index),     
      },
    },
    dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }
  public get ctr_max(): number 
  {
    return this.hard.ctr_max;
  }
  public set ctr_max(value: number) 
  {
    this.hard.ctr_max = value;
  }

  public get ev_max(): number 
  {
    return this.hard.ev_max;
  }
  public set ev_max(value: number) 
  {
    this.hard.ev_max = value;
  }

  public get amiss(): number 
  {
    return this.hard.amiss;
  }
  public set amiss(value: number) 
  {
    this.hard.amiss = value;
  }

}
