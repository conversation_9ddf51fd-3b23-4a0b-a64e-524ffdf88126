import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { AtributteDilemma } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class Dilemma
  extends Base<Data.Hard.IDilemma, Data.Result.IDilemma>
  implements Required<Data.Hard.IDilemma>
{
  public static generateId(optionBoxId: string, index?: number): string {
    return (
      optionBoxId +
      '.' +
      IdPrefixes.DILEMMA +
      (index !== undefined ? index : '_CANCEL')
    );
  }
  constructor(
    args: { index: number; optionBoxId: string; clonee?: Dilemma },
    dataAccess: Dilemma['TDataAccess']
  ) {
    const id = Dilemma.generateId(args.optionBoxId, args.index);
    if (args.clonee != null) {
      super({ hard: args.clonee.hard, newId: id }, dataAccess);
    } else {
      super({ hard: { id } }, dataAccess);
    }
  }
  protected getInternalFetch() {
    return {}; 
  }
  public get idDilemmaBox(): string {
    return this.hard.idDilemmaBox;
  }
  public set idDilemmaBox(value: string) {
    this.hard.idDilemmaBox = value;
  }
  public get isOmitted(): boolean {
    return this.hard.isOmitted;
  }
  public set isOmitted(value: boolean) {
    this.hard.isOmitted = value;
  }
  public get message(): string {
    return this.hard.message;
  }
  public set message(value: string) {
    this.hard.message = value;
  }
  public get weight(): number {
    return this.hard.weight ?? 0;
  }
  public set weight(value: number) {
    this.hard.weight = value;
  }
  public get label(): string {
    return this.hard.label;
  }
  public set label(value: string) {
    this.hard.label = value;
  }

  public get AndOrCondition(): string {
    return this.hard.AndOrCondition;
  }
  public set AndOrCondition(value: string) {
    this.hard.AndOrCondition = value;
  }
  public get threshold(): number {
    return this.hard.threshold;
  }
  public set threshold(value: number) {
    this.hard.threshold = value;
  }
  public get descriptionDilemmaPoints(): string {
    return this.hard.descriptionDilemmaPoints;
  }

  public set descriptionDilemmaPoints(value: string) {
    this.hard.descriptionDilemmaPoints = value;
  } 
  
  public get points(): AtributteDilemma[] {
    return this.hard.points;
  }
  public set points(value: AtributteDilemma[]) {
    this.hard.points = value;
  }  

}
