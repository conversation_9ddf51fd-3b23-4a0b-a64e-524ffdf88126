import { Character, Dialogue, Dilemma, DilemmaBox, Event, Level, Marker, OptionBox, Speech, StoryBox, Option } from 'src/app/lib/@bus-tier/models';
import { IdPrefixes, OptionBoxType } from '../../../../lib/darkcloud/dialogue-system/enums';
import { characterTypeName, dialogueTypeName, levelTypeColor } from '../../../../lib/darkcloud/dialogue-system/game-types/constants';
import { RoadBlock } from '../../@bus-tier/models/RoadBlock';

export function typeNameReview(obj: any, type: number): string 
{
  if (obj instanceof DilemmaBox) 
  {
    return 'Dilemma Box';
  } 
  if (obj instanceof Dilemma) 
  {
      return 'Choice Dilemma';
  }   
  if (type) {
    return type === OptionBoxType.CHOICE ? 'Choice Choice' : 'Choice Investigation';
  }  

  if (obj instanceof StoryBox) 
   {
    return obj.id.includes(IdPrefixes.OPTION) ? 'Choice Choice' : 'Story Box';
    //return type !== OptionBoxType.OPTION ? 'Answer Choice' : 'Story Box';
  } 
  else if (obj instanceof OptionBox) 
  {
    return +obj.type === OptionBoxType.CHOICE ? 'Choice Box' : 'Investigation Box';
  } 

  else if (obj instanceof Option) 
   {
    return type === OptionBoxType.CHOICE ? 'Choice Box' : 'Investigation Box';
  } 

  else if (obj instanceof Dialogue) 
  {
    return dialogueTypeName[+obj.type];
  } 
  else if (obj instanceof Speech) 
  {
    return 'Speech';

  } 
  /*
 else if (obj.id.includes(IdPrefixes.ANSWERDILEMMABOX)) 
    {
        return 'Speech Dilemma';
    }
*/

  else if (obj instanceof Marker) 
  {
   if (obj.type == 4 || obj.type == 8 || obj.type == 12 || obj.type == 0) return 'Boss Event';
    return 'Marker';
  } 
  else if (obj instanceof Event) 
  {  
   if (obj.type == 0 || obj.type == 1 || obj.type == 2 || obj.type == 14) return 'Item Event';
    else if (obj.type == 3 || obj.type == 6 || obj.type == 7) return 'Mission Event';
    else if (obj.type == 4 || obj.type == 5) return 'Cinematic Event';
    else if (obj.type == 11) return 'Loop Event'; 
    //return 'Event';
  } 
  else if (obj instanceof Option) 
  {
    return 'Option';
  } 
  else if (obj instanceof Character) 
  {
    return characterTypeName[+obj.type];
  }
  else if (obj instanceof RoadBlock) 
  {
    return 'RoadBlock';
  }
  return 'Nome não localizado';
}

/**
 * returns the display html color of an object as a string
 */
export function typeColorReview(obj, params?: { parent?: OptionBox }): string 
{
  if (obj.id.includes(IdPrefixes.ANSWERDILEMMABOX)) 
    {
        return '#1e1e1ef7';
    } 

  if (obj instanceof StoryBox && obj.id.includes(IdPrefixes.OPTION)) 
  {
    return '#CDAFDB';
    
  }
  
  
  if (obj instanceof StoryBox) 
    {
        return params.parent?.type === OptionBoxType.CHOICE ? '#CDAFDB' :
      params.parent?.type === OptionBoxType.INVESTIGATION ? '#c9e2de' : '#bdced9';
    } 

  else if (obj instanceof OptionBox) 
  {
    return +obj.type === OptionBoxType.CHOICE ? '#cdafdb' : '#9bc9ba';
  } 
  else if (obj instanceof Speech) 
  {
    return 'darkcyan';
  } 
  else if (obj instanceof Event) 
  {
    if (obj.type == 0 || obj.type == 1 || obj.type == 2) return '#ffc000';//Item Event
    if (obj.type == 3 || obj.type == 6 || obj.type == 7) return '#ed7d31'; //Mission Event
    if (obj.type == 4 || obj.type == 5) return '#7b7b7b'; // Cinematic Event
    if (obj.type == 11) return '#2c2c2c'; // Loop Event   
   // return '#FF9500';
  } 
  /* if(obj?.id.includes('mrk'))
    {
      let marker:Marker = this.MarkerService.models.svcFindById(obj.id);
      if(+marker.type == 4 || +marker.type == 8 || +marker.type == 12 || +marker.type == 0) return '#ff00bf';//BOSS Event    
    }
    */

  else if (obj instanceof Marker) 
  {
    return '#3472F7';
  } 
  else if (obj instanceof Option) 
  {
    return 'purple';
  } 
  else if (obj instanceof RoadBlock) 
  {
    return '#0ce730';
  } 
  else if (obj instanceof Level) 
  {
    return levelTypeColor[+obj.type];
  }
  return null
}

export function typeColorDilemma(obj): string {

  return '#1E1E1E';
}
