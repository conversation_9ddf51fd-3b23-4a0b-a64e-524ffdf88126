import { <PERSON><PERSON>, PipeTransform } from "@angular/core";
import { MarkerType } from "src/lib/darkcloud/dialogue-system";
import { MarkerService, OptionBoxService, OptionService, StoryBoxService, DilemmaBoxService, DilemmaService, AnswerDilemmaBoxService } from "../services";
import { Dialogue } from "../lib/@bus-tier/models";

@Pipe({
    name: 'dialogueTreeLayers'
})
/**
 * <PERSON><PERSON> that transforms a Dialogue object into an array of dialogue tree layers.
 * Each layer represents a dialogue box (StoryBox, OptionBox, DilemmaBox) with path information
 * for rendering branches and connections in the dialogue tree visualization.
 */
export class DialogueTreeLayers implements PipeTransform
{
  constructor(
    private _storyBoxService: StoryBoxService,
    private _optionBoxService: OptionBoxService,
    private _optionService: OptionService,
    private _markerService: MarkerService,
    private _dilemmaBoxService: DilemmaBoxService,
    private _dilemmaService: DilemmaService,
    private _answerDilemmaBoxService: AnswerDilemmaBoxService
  ) {}
    

  /**
   * Transform a dialogue into an array of dialogue tree layers with path information.
   * @param dialogue - The dialogue object to transform
   * @returns Array of dialogue tree layer objects with path and type information
   */
  transform(dialogue: Dialogue)
  {
    let deadEnd = false;
    let selfDead = false;
    let inPaths = undefined;
    let outPaths = [true];

    // Process each dialogue box and generate layer information
    let boxes = dialogue.boxIds.map(boxId =>
    {
      // Identify the type of dialogue box
      let storyBox = this._storyBoxService.svcFindById(boxId);
      let optionBox = this._optionBoxService.svcFindById(boxId);
      let dilemmaBox = this._dilemmaBoxService.svcFindById(boxId);

      // Update path information for this layer
      inPaths = outPaths;
      outPaths = [true];

      // Process StoryBox elements
      if(storyBox)
      {
        // Check for RESTART_DIALOGUE markers (takes precedence over FINISH_DIALOGUE)
        let hasRestartInStoryBox = false;
        storyBox.storyProgressIds.forEach(storyProgressId =>
        {
          let progressMarker = this._markerService.svcFindById(storyProgressId);
          if(progressMarker && progressMarker.type == MarkerType.RESTART_DIALOGUE)
          {
            hasRestartInStoryBox = true;
          }
        });

        // Check for FINISH_DIALOGUE markers only if no RESTART_DIALOGUE found
        if (!hasRestartInStoryBox) {
          storyBox.storyProgressIds.forEach(storyProgressId =>
          {
            let progressMarker = this._markerService.svcFindById(storyProgressId);
            if(progressMarker && progressMarker.type == MarkerType.FINISH_DIALOGUE)
            {
              // FINISH_DIALOGUE logic is currently commented out but preserved for future use
            }
          });
        }
        outPaths = [!deadEnd];
      }

        //Treats the CHOICE/INVESTIGATION BOXES and their children
        if(optionBox)
        {
          outPaths = [];
          let closedCount = 0;
          optionBox.optionIds.forEach(optionId => 
          {
            let option = this._optionService.svcCloneById(optionId);
            let optionClosed = false;
            if(option)
            {
              let box = this._storyBoxService.svcFindById(option.answerBoxId);

              // First pass: check for RESTART_DIALOGUE (takes precedence)
              let hasRestartInOption = false;
              box.storyProgressIds.forEach(storyProgressId =>
              {
                let progressMarker = this._markerService.svcFindById(storyProgressId);
                if(progressMarker && progressMarker.type == MarkerType.RESTART_DIALOGUE)
                {
                  hasRestartInOption = true;
                  // For options with RESTART_DIALOGUE, treat like closed (no branch continuation)
                  closedCount++;
                  optionClosed = true;
                }
              });

              // Second pass: only check for FINISH_DIALOGUE if no RESTART_DIALOGUE found
              if (!hasRestartInOption) {
                box.storyProgressIds.forEach(storyProgressId =>
                {
                  let progressMarker = this._markerService.svcFindById(storyProgressId);
                  if(progressMarker && progressMarker.type == MarkerType.FINISH_DIALOGUE)
                  {
                    closedCount++;
                    optionClosed = true;
                  }
                });
              }
            }

            // Add path for the regular option
            outPaths.push(!optionClosed);

            // If this option has a dice negative outcome, add another path for it
            if (option && option.answerBoxNegativeId) {
              // Check if the negative outcome is also closed
              let negativeBox = this._storyBoxService.svcFindById(option.answerBoxNegativeId);
              let negativeOptionClosed = false;

              if (negativeBox) {
                // Check for RESTART_DIALOGUE in negative outcome
                let hasRestartInNegative = false;
                negativeBox.storyProgressIds.forEach(storyProgressId => {
                  let progressMarker = this._markerService.svcFindById(storyProgressId);
                  if (progressMarker && progressMarker.type == MarkerType.RESTART_DIALOGUE) {
                    hasRestartInNegative = true;
                    negativeOptionClosed = true;
                  }
                });

                // Check for FINISH_DIALOGUE in negative outcome if no RESTART_DIALOGUE
                if (!hasRestartInNegative) {
                  negativeBox.storyProgressIds.forEach(storyProgressId => {
                    let progressMarker = this._markerService.svcFindById(storyProgressId);
                    if (progressMarker && progressMarker.type == MarkerType.FINISH_DIALOGUE) {
                      negativeOptionClosed = true;
                    }
                  });
                }
              }

              // Add path for the dice negative outcome
              outPaths.push(!negativeOptionClosed);
            }
          });

          if (closedCount >= optionBox.optionIds.length)
          {
            if(!selfDead)
            {
              selfDead = true;
            }
            else
            {
              deadEnd = true;
            }
          }
        }

        //Treats the DILEMMA BOXES and their children
        if(dilemmaBox)
        {
          outPaths = [];
          let closedCount = 0;
          dilemmaBox.optionDilemmaIds.forEach(dilemmaId => 
          {
            let dilemma = this._dilemmaService.svcCloneById(dilemmaId);
            let dilemmaClosed = false;
            if(dilemma)
            {
              let box = this._answerDilemmaBoxService.svcFindById(dilemma.idDilemmaBox);

              // First pass: check for RESTART_DIALOGUE (takes precedence)
              let hasRestartInDilemma = false;
              box.storyProgressIds.forEach(storyProgressId =>
              {
                let progressMarker = this._markerService.svcFindById(storyProgressId);
                if(progressMarker && progressMarker.type == MarkerType.RESTART_DIALOGUE)
                {
                  hasRestartInDilemma = true;
                  // For dilemmas with RESTART_DIALOGUE, treat like closed (no branch continuation)
                  closedCount++;
                  dilemmaClosed = true;
                }
              });

              // Second pass: only check for FINISH_DIALOGUE if no RESTART_DIALOGUE found
              if (!hasRestartInDilemma) {
                box.storyProgressIds.forEach(storyProgressId =>
                {
                  let progressMarker = this._markerService.svcFindById(storyProgressId);
                  if(progressMarker && progressMarker.type == MarkerType.FINISH_DIALOGUE)
                  {
                    closedCount++;
                    dilemmaClosed = true;
                  }
                });
              }
            }
            outPaths.push(!dilemmaClosed);
          });

          if (closedCount >= dilemmaBox.optionDilemmaIds.length)
          {
            if(!selfDead)
            {
              selfDead = true;
            }
            else
            {
              deadEnd = true;
            }
          }
        }

        if(deadEnd)
        {
          outPaths = [false];
        }
        return {id: boxId, deadEnd: deadEnd, layerType: 'default', inPaths: inPaths, outPaths: outPaths};
      })
      inPaths = outPaths;
      outPaths = [true];
      //The START green node in the tree.
      boxes.unshift({id: undefined, deadEnd: false, layerType: 'start', inPaths: undefined, outPaths: [true]});
      //The END red node in the tree.
      boxes.push({id: undefined, deadEnd: deadEnd, layerType: 'end', inPaths: inPaths, outPaths: outPaths});
      //The Restart Dialogue node in the tree
      boxes.push({id: undefined, deadEnd: deadEnd, layerType: 'restart', inPaths: inPaths, outPaths: outPaths});
      return boxes;
    }
}
