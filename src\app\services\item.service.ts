import { UserSettingsService } from 'src/app/services/user-settings.service';
import { IndexStorageService } from './index-storage.service';
import { Injectable } from '@angular/core';
import { Area, Item } from 'src/app/lib/@bus-tier/models';
import { Alert } from 'src/lib/darkcloud';
import { EventService } from './event.service';
import { ReviewService } from './review.service';
import { EventType, ItemType } from 'src/lib/darkcloud/dialogue-system';
import { ModelService } from 'src/lib/darkcloud/angular/dsadmin';
import { ItemClassService } from './item-class.service';
import { AreaService } from './area.service';
import { Event } from 'src/app/lib/@bus-tier/models';
import { RoadBlock } from '../lib/@bus-tier/models/RoadBlock';
import { RoadBlockService } from './road-block.service';
import { ICounterItems } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { OpenAIEnvironmentService } from './openAi-environment.service';
import { RevisorAI } from '../components/admin-layout/components/router/components/settings/ai-revisor/ai-revisor.component';

export interface ReviewMessage 
{
  typeName: string;
  modelId: string;
  reviewTypeId: string;
  message: string;
}

@Injectable({
  providedIn: 'root',
})
export class ItemService extends ModelService<Item> 
{
  public readonly ItemTypes = [
    ItemType.CURRENCY,
    ItemType.POTION,
    ItemType.PROBLEM,
    ItemType.KEY
  ];

  constructor(
    private _eventService: EventService,
    private _openAIEnvironmentService: OpenAIEnvironmentService,
    private _roadBlockService: RoadBlockService,
    protected override _reviewService: ReviewService,
    indexStorageService: IndexStorageService,
    readonly userSettingsService: UserSettingsService,
    private _itemClassService: ItemClassService,
    private _areaService: AreaService,
  ) {
    super(
      {
        defaultConstructor: () =>
          new Item(0, undefined, undefined, this.userSettingsService),
      },
      'Item',
      indexStorageService,
      _reviewService
    );
  }


  public async svcPromptCreateNew(type: ItemType): Promise<Item> 
  {
    let name: string = await Alert.showPrompt('Item Name');
    
    if (!name) return null;   

    let newItem = new Item(this.svcNextIndex(), name, type, this.userSettingsService)
    if(this.models.find(x => x.name == newItem.name))
    {
      throw new Error('Item already exists');
    }
    return newItem;
  }

  public override async toPromptRemove(item: Item): Promise<boolean> 
  {
    const names: string[] = this._reviewService.reviewResults[item.id].givenAt
      .concat(this._reviewService.reviewResults[item.id].receivedAt)
      .concat(this._reviewService.reviewResults[item.id].tradedAt);

    let confirm = false;

    if (names.length === 0) 
    {
      confirm = await Alert.showRemoveAlert(item.name);
    } 
    else 
    {
      confirm = await Alert.showConfirm(
        'Are you sure you want to remove \n"' + item.name + '"?',
        'This will remove the item from ' + names.length + ' events',
        'Review'
      ).then(async (c) => 
      {
        if (!c) return null;

        return await Alert.showReviewUnlink(item.name + ' will be removed from:', names);
      });
    }
    if (confirm) 
    {
      await this.svcToRemove(item.id);
      return true;
    }
    return false;
  }

  protected override svcHasErrors(item: Item): boolean 
  {
    this._reviewService.reviewResults[item.id] = 
    {
      givenAt: [],
      receivedAt: [],
      tradedAt: [],
      assignedAt: [],
    };
    this._eventService
      .filterByRequirement('itemId', item.id)
      .forEach((event) => 
      {
        this._reviewService.reviewResults[item.id].assignedAt.push(event.id);
        switch (+event.type) 
        {
          case EventType.GIVE_ITEM:
            this._reviewService.reviewResults[item.id].givenAt.push(event.id);
            break;
          case EventType.RECEIVE_ITEM:
            this._reviewService.reviewResults[item.id].receivedAt.push(event.id);
            break;
        case EventType.DIARY_EVENT:
              this._reviewService.reviewResults[item.id].diaryAt.push(event.id);
              break;
            case EventType.TRADE_ITEM:
              this._reviewService.reviewResults[item.id].tradedAt.push(event.id);
              break;
        }
      });
    if (this._reviewService.reviewResults[item.id].assignedAt.length === 0 ||
      this._reviewService.reviewResults[item.id].receivedAt.length === 0) return true;
    
    return false;
  }

  protected override svcErrors(item: Item): ReviewMessage[] 
  {
    this._reviewService.reviewResults[item.id] = 
    {
      givenAt: [],
      receivedAt: [],
      tradedAt: [],
      assignedAt: [],
    };
    this._eventService
      .filterByRequirement('itemId', item.id)
      .forEach((event) => 
      {
        this._reviewService.reviewResults[item.id].assignedAt.push(event.id);
        switch (+event.type) 
        {
          case EventType.GIVE_ITEM:
            this._reviewService.reviewResults[item.id].givenAt.push(event.id);
            break;
          case EventType.RECEIVE_ITEM:
            this._reviewService.reviewResults[item.id].receivedAt.push(
              event.id
            );
            break;
            case EventType.TRADE_ITEM:
              this._reviewService.reviewResults[item.id].tradedAt.push(
                event.id
              );
              break;
           case EventType.DIARY_EVENT:
                this._reviewService.reviewResults[item.id].diaryAt.push(
                  event.id
                );
                break;
        }
      });
    if (this._reviewService.reviewResults[item.id].assignedAt.length === 0 ||
      this._reviewService.reviewResults[item.id].receivedAt.length === 0) 
    {
      return [{typeName: this.typeName, modelId: item.id, reviewTypeId: 'NotAssign', message: item.name + ' is not assigned'}];
    }
    return undefined;
  }

  // also removes from the events attached to the items
  protected override async svcUnlink(items: Item[]) 
  {
    const itemIds: string[] = [];
    items.forEach((item) => 
    {
      itemIds.push(item.id);
    });
    await this._eventService.RemoveRequirementValues('itemId', itemIds);
    this._itemClassService.models.forEach(x => 
    {
      if(x.itemIds?.filter(x => itemIds.includes(x)).length > 0)
      {
        x.itemIds = x.itemIds.filter(x => !itemIds.includes(x));
        this._itemClassService.svcToModify(x);
      }
    });
  }

  protected override svcVerify(item: Item) 
  {
    if ((item.property && (item.type === ItemType.CURRENCY || item.type === ItemType.KEY)) || !item.property) 
    {
      item.property = undefined;
    }
  }

  public svcGetAllEventAssigned(id: string): Event[] 
  {
    return this._eventService
      .models
      .filter((ev) => ev.itemId == id)
      .filter((ev) => ev.type == EventType.GIVE_ITEM || ev.type == EventType.RECEIVE_ITEM || ev.type == EventType.TRADE_ITEM || ev.type == EventType.DIARY_EVENT);
  }

  public svcGetAllRoadBlockAssigned(id: string): RoadBlock[] 
  {
    return this._roadBlockService.models.filter((rb) => rb.ItemID === id)
  }

  public svcGetAllAreasAssigned(id: string): Area[] 
  {
    let ids = [];
    let areas:Area[] = [];
    let events = this.svcGetAllEventAssigned(id);
    events.forEach(ev => 
    {
      let idFiltered = ev.id.slice(0, ev.id.indexOf('.'));
      ids.push(idFiltered);
    })
   
    ids.forEach(id => 
    {
      let area = this._areaService.svcFindById(id);
      if(area)
        areas.push(area);
    })
    return areas;
  }

  public svcGetTagsUsed(items: Item[]): string[]
  {
    let usedTagsId: string[] = [];
    items.forEach(item => 
    {
      if(item.tagIds)
        item.tagIds.forEach(tagId => 
        {
          if(!usedTagsId.includes(tagId))
            usedTagsId.push(tagId);
        })
    })
    return usedTagsId;
  }

  // AI Revisor
itemsLitsCounter(): ICounterItems[] {
  const baseSize = this.models.length;
  const counterItems: ICounterItems[] = [];

  const result = this.models.reduce((acc, current) => {
    acc.revisedItems += (current.isReviewedName ? 1 : 0) + (current.isReviewedDescription ? 1 : 0);
    acc.unreviewedItems += (current.name && !current.isReviewedName) ? 1 : 0;
    acc.unreviewedItems += (current.description && !current.isReviewedDescription) ? 1 : 0;
    acc.emptyFields += (current.name === "" || current.name === undefined) ? 1 : 0;
    acc.emptyFields += (current.description === "" || current.description === undefined) ? 1 : 0;
    return acc;
  }, {
    revisedItems: 0,
    unreviewedItems: 0,
    emptyFields: 0
  });

  counterItems.push({
    name: 'Items List',
    baseSize: baseSize,
    totalFields: baseSize * 2,
    totalEmptyFields: result.emptyFields,
    unreviewedItems: result.unreviewedItems,
    revisedItems: result.revisedItems,
    suggestions: 0,
    totalNoSuggestions: 0,
    suggestionsRejected: 0,
  });

  return counterItems;
}
  
  checkCounterItemsList(idEnvironmentAi: string) {
    const itemsToModify: typeof this.models = [];
    const limit = this._openAIEnvironmentService.models.find(x => x.id === idEnvironmentAi).limitForReview;//limite de revisão
  
    for (const item of this.models) {
      let modified = false;
  
      if (item.revisionCounterNameAI >= limit && !item.isReviewedName) {
        item.isReviewedName = true;
        modified = true;
      }
  
      if (item.revisionCounterDescriptionAI >= limit && !item.isReviewedDescription) {
        item.isReviewedDescription = true;
        modified = true;
      }
  
      if (modified) {
        itemsToModify.push(item);
      }
    }
  
    for (const item of itemsToModify) {
      this.svcToModify(item);
    }
  }
  
  addCounterItemsList(itemsListRevisor: RevisorAI[], idEnvironmentAi: string) {
    const env = this._openAIEnvironmentService.models.find(x => x.id === idEnvironmentAi);
    if (!env) return;
  
    const limit = env.limitForReview;  
  
    for (let index = 0; index < itemsListRevisor.length; index++) {
      const revisor = itemsListRevisor[index];
      const itemsList = this.models.find(x => x.id === revisor.id);
      if (!itemsList) continue;
  
      if (revisor?.name) {
        const nameCount = itemsList.revisionCounterNameAI ?? 0;
        if (nameCount < limit) {
          itemsList.revisionCounterNameAI = nameCount + 1;
        } 
        else {
          itemsList.isReviewedName = true;
        }
      }
      else {
        const descCount = itemsList.revisionCounterDescriptionAI ?? 0;
        if (descCount < limit) {
          itemsList.revisionCounterDescriptionAI = descCount + 1;
        } 
        else {
          itemsList.isReviewedDescription = true;      
      } 
    }
      this.svcToModify(itemsList);    
    }
  }
}
