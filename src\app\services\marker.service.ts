import { Injectable } from '@angular/core';
import { Dialogue, Level, Marker } from 'src/app/lib/@bus-tier/models';
import { Data, ModelService } from 'src/lib/darkcloud/angular/dsadmin';
import { getMarkerRequirementsByType, invalidRequirementsByMarkerTypes, MarkerParameters } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { MarkerMessageType, MarkerType } from 'src/lib/darkcloud/dialogue-system';
import { IndexStorageService } from './index-storage.service';
import { ReviewService } from './review.service';
import { UserSettingsService } from './user-settings.service';

@Injectable({
  providedIn: 'root',
})
export class MarkerService extends ModelService<Marker> 
{
  constructor(
    protected override _reviewService: ReviewService,
    indexStorageService: IndexStorageService,
    readonly userSettingsService: UserSettingsService
  ) 
  {
    super(
      {
        defaultConstructor: () =>
          new Marker(
            { index: 0, storyBoxId: undefined },
            this.userSettingsService
          ),
      },
      'Marker',
      indexStorageService,
      _reviewService
    );
  }
  public readonly markerTypes = [
    MarkerType.UNLOCK_LEVEL,
    MarkerType.RELEASE_DIALOGUE,
    MarkerType.PIN,
    MarkerType.MARK_COLLECTIBLE,
    MarkerType.BLOCK_BATTLE,
    MarkerType.FINISH_DIALOGUE,
    MarkerType.RESTART_DIALOGUE,
    MarkerType.BLOCK_MAP,
    MarkerType.BLOCK_GRIND,
    MarkerType.ATTACK_ADVANTAGE,
    MarkerType.KNOWLEDGE_POINT,
  ];

  public readonly markerMessageTypes = [
    MarkerMessageType.CHARACTER,
    MarkerMessageType.MISION,   
  ];

  protected override svcHasErrors(marker: Marker): boolean 
  {
    this._reviewService.reviewResults[marker.id] = { parametersWithErrors: [] };
    this._reviewService.reviewResults[marker.id].parametersWithErrors = this.requirementsMissing(marker);
    this._reviewService.reviewResults[marker.id].levelId = Level.getSubIdFrom(marker.id);
    this._reviewService.reviewResults[marker.id].dialogueId = Dialogue.getSubIdFrom(marker.id, 'PT-BR');

    if (this._reviewService.reviewResults[marker.id].parametersWithErrors.length > 0) return true;
    
    return false;
  }

  protected override svcAfterModifyReviewRequest(marker: Marker, oldMarker?: Marker) 
  {
    this._reviewService.reviewRequestInfo.forEach((info) => 
    {
      if (marker[info.parameter]) 
      {
        this._reviewService.requestReview(
        {
          typeName: info.typeName,
          parameters: { objectId: marker[info.parameter] },
        });
        if (oldMarker && oldMarker[info.parameter]) 
        {
          this._reviewService.requestReview(
          {
            typeName: info.typeName,
            parameters: { objectId: oldMarker[info.parameter] },
          });
        }
      }
    });
  }

  protected override svcVerify(marker: Marker) 
  {
    if (marker.type === undefined) return;
      
    // deletes unnecessary parameters by preserving the ones that the marker type requires
    const values: { [requirement: string]: any } = {};
    getMarkerRequirementsByType(invalidRequirementsByMarkerTypes, marker.type).forEach((param) => 
    {
      values[param.key] = marker[param.key];
    });

    // either undefined or value;
    marker.characterId = values['characterId'];
    marker.pin = values['pin'];
    marker.levelId = values['levelId'];  

    if (+marker.type === MarkerType.BLOCK_BATTLE && marker.levelId === undefined) 
    {
      marker.levelId = Level.getSubIdFrom(marker.id);
    }
    
  }

  public async svcPromptCreateNew(storyBoxId: string): Promise<Marker> 
  {
    return new Marker(
      { 
        index: this.svcNextIndex(), storyBoxId 
      },
      this.userSettingsService
    );
  }

  // filters markers that matches a MARKER TYPE
  public filterByType(markerTypes: MarkerType | MarkerType[]): Marker[] 
  {
    const types = [].concat(markerTypes);
    let markers: Marker[] = [];
    types.forEach((type) => 
    {
      markers = markers.concat(this.models.filter((o) => +o.type === type));
    });
    return markers;
  }

//Validates the fields of the Marker component and adds it to the Review in Missiong  
  public requirementsMissing(marker: Marker): string[] 
  {
    const missing: string[] = [];  
    if (marker.type === undefined || marker.type == +MarkerType.REMOVE_SPECIAL_ITEM) 
    {      
      missing.push('type');
    } 
   
    else 
    {
      getMarkerRequirementsByType(invalidRequirementsByMarkerTypes, marker.type).forEach((param) => 
      { 
        if (param.invalidValues.includes(marker[param.key])) 
        {
          missing.push(param.key);
        }
      });
    }    
 
    if (marker.type == MarkerType.RESTART_DIALOGUE) {

      if(marker.unlockCondition == 'Defeated Boss' || marker.unlockCondition == 'Talked to Character' || 
        marker.unlockCondition == 'Collected Class' || marker.unlockCondition == 'Spoke In')
        {
        if(marker.choosedCondition == undefined || marker.choosedCondition == "") missing.push('choosedCondition');
      }
      
      if(marker.unlockCondition == "Obtained Item") {
        if(marker.choosedCondition == undefined || marker.choosedCondition == "")  missing.push('choosedCondition');
        if(marker.conditionOperator == undefined || marker.conditionOperator == "") missing.push('conditionOperator');
        if(marker.amountRequired == undefined) missing.push('amountRequired');
      }  

      if(marker.unlockCondition == "Karmic Equilibrium") {
        if(marker.choosedCondition == undefined || marker.choosedCondition == "") missing.push('choosedCondition');
        if(marker.amountRequired == undefined) missing.push('amountRequired');
      }

    }
    

    return missing;
  }

  public filterByRequirement(requirement: keyof MarkerParameters, value: string): Marker[] 
  {
    return this.models.filter((marker) => value === marker[requirement]);
  }

  public async RemoveRequirementValues(
    requirement: keyof Data.Hard.IMarker & Omit<keyof Data.Hard.IMarker, 'id'>,
    value: string | string[]
  ) 
  {
    const values = [].concat(value);
    const markerIds: string[] = [];
    this.models.forEach((marker) => 
    {
      if (values.includes(marker[requirement])) 
      {
        marker[requirement] = undefined;
        markerIds.push(marker.id);
      }
    });
    this.svcReviewThese(markerIds);
    await this.toSave();
  }

  public filterByValues(type: MarkerType, ...values: string[]): Marker[] 
  {
    return this.filterByType(type).filter(
      (marker) =>
        values.length >=
      getMarkerRequirementsByType(invalidRequirementsByMarkerTypes, type).filter(
          (param) => values.find((value) => marker[param.key] === value)
        ).length
    );
  }

  async toAddReplica(source: Marker, storyBoxId: string): Promise<Marker> 
  {
    const cloned = this.replicate(storyBoxId, source);
    await this.srvAdd(cloned);
    return cloned;
  }

  public replicate(storyBoxId: string, clonee: Marker) 
  {
    return new Marker(
      { 
        index: this.svcNextIndex(), storyBoxId, clonee 
      },
      this.userSettingsService
    );
  }

  filterListItem(event, itemList, type)
  {
    let displayItemList = [];

    if (!event || event == '') 
    {
      displayItemList = itemList;
    } 
    else if (typeof event === 'string') 
    {
      itemList.forEach(item => 
      {
        if(item[type].toLocaleLowerCase().includes(event.toLocaleLowerCase().trim()))
        {
          displayItemList.push(item);
        }
      });
    }

    return displayItemList;
  } 
}
