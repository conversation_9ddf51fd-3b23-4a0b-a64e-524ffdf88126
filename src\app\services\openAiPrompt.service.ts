import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { catchError, Observable, throwError } from 'rxjs';
import { RevisorAI } from '../components/admin-layout/components/router/components/settings/ai-revisor/ai-revisor.component';
import { Alert } from 'src/lib/darkcloud';
import { Tiktoken } from '@dqbd/tiktoken/lite';
import cl100k_base from '@dqbd/tiktoken/encoders/cl100k_base';
import { AIPrompt } from '../lib/@bus-tier/models/AIPrompt';
import { OpenAIEnvironmentService } from './openAi-environment.service';
import { IArrayOption } from '../components/admin-layout/components/router/components/level-and-dialogue/components/level-dialogue-editor/components/speech/modal-ai-speeches/modal-ai-speeches.component';

@Injectable({
  providedIn: 'root'
})
export class OpenAiPromptService {
  private apiUrl = 'https://api.openai.com/v1/chat/completions';
  //private apiKey = '********************************************************************************************************************************************************************'; 
  constructor(
    private http: HttpClient, 
    private _openAIEnvironmentService: OpenAIEnvironmentService,) {}

getChatResponse(prompt: any, searchContent: string): Observable<any> {

  const environment = this._openAIEnvironmentService.svcFindById(prompt.idEnvironmentAi);

  const headers = new HttpHeaders({
    'Content-Type': 'application/json',
    Authorization: `Bearer ${environment.apiKey}`
  });

  const body = {
    model: environment.model,
    messages: [
      { role: 'user', content: prompt.prompt },
      { role: 'user', content: searchContent }
    ],
    max_tokens: 2048,
    temperature: environment.temperature
  };

  return this.http.post(this.apiUrl, body, { headers }).pipe(
    catchError(error => {
      console.error('Erro na API OpenAI:', error);        
      if (error.error) {
        console.error('Detalhes do erro:', JSON.stringify(error.error, null, 2));
      }        
      return throwError(() => new Error(error.message || 'Erro desconhecido na API OpenAI'));
    })
  );    
}

getChatGhostwriterResponse(prompt: IArrayOption, searchContent: { text: string }[]): Observable<any> { 

  const environment = this._openAIEnvironmentService.svcFindById(prompt.idEnvironmentAi);

  const headers = new HttpHeaders({
    'Content-Type': 'application/json',
    Authorization: `Bearer ${environment.apiKey}`
  });

  const combinedSearchContent = searchContent.map(item => item.text).join('\n');

  const body = {
    model: environment.model,
    messages: [
      { role: 'user', content: prompt.prompt },
      { role: 'user', content: combinedSearchContent }   
    ],
    max_tokens: 2048,
    temperature: environment.temperature
  };

  return this.http.post(this.apiUrl, body, { headers }).pipe(
    catchError(error => {
      console.error('Erro na API OpenAI:', error);
      if (error.error) {
        console.error('Detalhes do erro:', JSON.stringify(error.error, null, 2));
      }
      return throwError(() => new Error(error.message || 'Erro desconhecido na API OpenAI'));
    })
  );
}

  /**
 * Divide a lista em lotes com base na estimativa de tokens.
 */
async dividirLotesPorTokens(
  lista: RevisorAI[],
  prompt: string,
  limiteTokensModelo: number = 8192,
  maxItensPorLote: number = 20,
  verbose: boolean = false
): Promise<RevisorAI[][]> {
  const lotes: RevisorAI[][] = [];
  let loteAtual: RevisorAI[] = [];
  let tokensAtuais = 0;

  const encoder = new Tiktoken(
    cl100k_base.bpe_ranks,
    cl100k_base.special_tokens,
    cl100k_base.pat_str
  );

  const promptCompleto = `${prompt.trim()}\n\n${this.extraInstructions().trim()}`;
  const tokensPrompt = encoder.encode(promptCompleto).length;

  let tokensParaResposta = 1000;
  if (tokensPrompt < 1000) tokensParaResposta = 2000;
  else if (tokensPrompt > 3000) tokensParaResposta = 512;

  const limiteTokensPrompt = limiteTokensModelo - tokensPrompt - tokensParaResposta;
/*
  if (verbose) {
    console.log(`\u2728 Prompt tokens: ${tokensPrompt}`);
    console.log(`\u2705 Tokens reservados para resposta: ${tokensParaResposta}`);
    console.log(`\u25B6\ufe0f Tokens disponíveis por lote: ${limiteTokensPrompt}`);
  }
  */

  for (const item of lista) {
    const textoItem = JSON.stringify(item);
    const tokensItem = encoder.encode(textoItem).length;

    if (loteAtual.length >= maxItensPorLote || tokensAtuais + tokensItem > limiteTokensPrompt) {
      if (loteAtual.length > 0) {
        lotes.push(loteAtual);
     //   if (verbose) console.log(`\uD83D\uDCE6 Lote ${lotes.length}: ${tokensAtuais} tokens / ${loteAtual.length} itens`);
        loteAtual = [];
        tokensAtuais = 0;
      }
    }

    loteAtual.push(item);
    tokensAtuais += tokensItem;
  }

  if (loteAtual.length > 0) {
    lotes.push(loteAtual);
  //  if (verbose) console.log(`\uD83D\uDCE6 Lote ${lotes.length}: ${tokensAtuais} tokens / ${loteAtual.length} itens`);
  }

  if (verbose) console.log(`\uD83D\uDD04 Total de lotes gerados: ${lotes.length}`);

  encoder.free();
  return lotes;
}
  /**
   * Conta o número estimado de tokens de um lote de objetos.
   */
  contarTokensLote(lote: any): number {
    const encoder = new Tiktoken(
      cl100k_base.bpe_ranks,
      cl100k_base.special_tokens,
      cl100k_base.pat_str
    );
    const total = encoder.encode(JSON.stringify(lote)).length;
    encoder.free();
    return total;
  }

  extraInstructions() {
    const instructions = `
    Você receberá um array de objetos JSON contendo campos como: "id", "name", "title", "description", "message" e/ou "word". 
    Sua tarefa:
    - Revise gramaticalmente os textos nos campos "name", "title", "description", "message" e "word", corrigindo erros de ortografia, acentuação, pontuação ou concordância.
    - Corrija também o conteúdo dentro dos seguintes delimitadores especiais: [[ ]], {{ }}, «« »», << >> e #$ $#, mas **sem removê-los ou modificá-los**.
    - Corrija apenas o conteúdo interno se houver erro; preserve completamente a estrutura externa dos delimitadores.
    - Se um campo tiver alguma correção, adicione um novo campo correspondente com prefixo "new". Exemplos:
      - Correção em "name" → adicionar "newName".
      - Correção em "title" → adicionar "newTitle".
      - Correção em "description" → adicionar "newDescription".
      - Correção em "message" → adicionar "newMessage".
      - Correção em "word" → adicionar "newWord".
    - Mantenha sempre o campo "id" e o campo original. Não altere a estrutura do objeto original.
    
    ❗ Importante:
    - **Preserve os delimitadores especiais** como estão: [[ ]], {{ }}, «« »», << >> e #$ $#.
    - **Revise apenas o texto interno desses delimitadores se houver erro**. Exemplo: [[palavraa]] → [[palavra]]
    - Se não houver nenhum erro no objeto, **não inclua o objeto na resposta**.
    - A resposta deve conter **apenas os objetos que tiveram alguma correção**.
    - A resposta final deve ser um **array JSON válido**, **sem nenhum comentário ou texto fora dos objetos**.
    
    ✅ Exemplo de saída válida:
    [
      {
        "id": "ABC123",
        "message": "Texto com erro como [[palavraa]].",
        "newMessage": "Texto com erro como [[palavra]]."
      }
    ]
    `;
  
    return instructions;
  }
  

// API usada na tela do AI REVISOR
getRevisorAIPrompt(prompt: AIPrompt, searchContent: RevisorAI[]): Observable<any> {
  if (typeof prompt.prompt !== 'string') {
    throw new Error('Prompt deve ser uma string');
  }

  const environment = this._openAIEnvironmentService.svcFindById(prompt.idEnvironmentAi);

  const headers = new HttpHeaders({
    'Content-Type': 'application/json',
    Authorization: `Bearer ${environment.apiKey}`
  });

  const body = {
    model: environment.model, // gpt-4.1-mini
    temperature: environment.temperature, //0.2
    top_p: 1,
    max_tokens: 4096,
    messages: [
      {
        role: 'system',
               content: 'Você é um revisor gramatical preciso. Corrige erros sem alterar ordem das palavras, segue regras rígidas de capitalização e pontuação.'
      },
      {
        role: 'user',
        content: `${prompt.prompt.trim()}\n\n${this.extraInstructions().trim()}\n\n${JSON.stringify(searchContent, null, 2)}`
      }
    ]
  };

  return this.http.post(this.apiUrl, body, { headers }).pipe(
    catchError(error => {
      console.error('Erro na API OpenAI:', error);
      if (error.error) {
        console.error('Detalhes do erro:', JSON.stringify(error.error, null, 2));
        Alert.showError(error.error.error.message, 'Erro no servidor da OpenAI');
      }
      return throwError(() => new Error(error.message || 'Erro desconhecido na API OpenAI'));
    })
  );
}


extraInstructionsObject() {
  const instructions = `
Regras:
1. Mantenha a mesma estrutura da entrada.
2. Para cada campo com array de frases (ex: "light", "moderate", "critical", "factor", "description"), retorne uma nova chave chamada "new<NomeDoCampo>".
3. Se a frase estiver correta, retorne a posição correspondente do array "new<NomeDoCampo>" com uma string vazia ("").
4. A estrutura final deve manter o campo "id", os campos originais e os novos campos "new<NomeDoCampo>".
5. Não adicione comentários ou explicações fora do JSON.
    `;
return instructions;
}


//API para envio de objetos para revisão
getRevisorAIPromptObject(prompt: AIPrompt, searchContent: any[]): Observable<any> {
  if (typeof prompt.prompt !== 'string') {
    throw new Error('Prompt deve ser uma string');
  }
 
  const environment = this._openAIEnvironmentService.svcFindById(prompt.idEnvironmentAi);
 
  const headers = new HttpHeaders({
    'Content-Type': 'application/json',
    Authorization: `Bearer ${environment.apiKey}`
  });

  const body = {
    model: environment.model, // gpt-4.1-mini
    temperature: environment.temperature, //0.2
    top_p: 1,
    max_tokens: 4096,
    messages: [
      {
        role: 'system',
               content: 'Você é um revisor gramatical rigoroso. Corrija erros de ortografia, acentuação, concordância e pontuação, mas sem alterar a ordem das palavras nem o estilo da frase.'
      },
      {
        role: 'user',
        content: `${prompt.prompt.trim()}\n\n${this.extraInstructionsObject().trim()}\n\n${JSON.stringify(searchContent, null, 2)}`
      }
    ]
  };

  return this.http.post(this.apiUrl, body, { headers }).pipe(
    catchError(error => {
      console.error('Erro na API OpenAI:', error);
      if (error.error) {
        console.error('Detalhes do erro:', JSON.stringify(error.error, null, 2));
        Alert.showError(error.error.error.message, 'Erro no servidor da OpenAI');
      }
      return throwError(() => new Error(error.message || 'Erro desconhecido na API OpenAI'));
    })
  );
}


getDilemmaPointsAIResponse(prompt: AIPrompt, searchContent: string): Observable<any> {

  const environment = this._openAIEnvironmentService.svcFindById(prompt.idEnvironmentAi);

  const headers = new HttpHeaders({
    'Content-Type': 'application/json',
    Authorization: `Bearer ${environment.apiKey}`
  });

  const body = {
    model: environment.model,
    messages: [
      { role: 'user', content: prompt.prompt },
      { role: 'user', content: searchContent }
    ],
    max_tokens: 2048,
    temperature: environment.temperature
  };

  return this.http.post(this.apiUrl, body, { headers }).pipe(
    catchError(error => {
      console.error('Erro na API OpenAI:', error);        
      if (error.error) {
        console.error('Detalhes do erro:', JSON.stringify(error.error, null, 2));
      }        
      return throwError(() => new Error(error.message || 'Erro desconhecido na API OpenAI'));
    })
  );    
}

}
