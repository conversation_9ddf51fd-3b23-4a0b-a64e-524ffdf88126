import { UserSettingsService } from 'src/app/services/user-settings.service';
import { IndexStorageService } from './index-storage.service';
import { Injectable } from '@angular/core';
import { OptionBox, Option, Dialogue } from 'src/app/lib/@bus-tier/models';
import { OptionService } from './option.service';
import { OptionBoxType } from 'src/lib/darkcloud/dialogue-system';
import { ModelService } from 'src/lib/darkcloud/angular/dsadmin';
import { StoryBoxService } from './story-box.service';
import { SpeechService } from './speech.service';
import { EventService } from './event.service';
import { MarkerService } from './marker.service';

@Injectable({
  providedIn: 'root',
})
export class OptionBoxService extends ModelService<OptionBox> {

   public usedOptionsPerBox: Map<string, Set<string>> = new Map();

  clearSession() {
    this.usedOptionsPerBox.clear();
  }
  

  constructor(
    private _optionService: OptionService,
    private _storyBoxService: StoryBoxService,
    private _speechService: SpeechService,
    private _eventService: EventService,
    private _markerService: MarkerService,
    indexStorageService: IndexStorageService,
    readonly userSettingsService: UserSettingsService
  ) {
    super(
      {
        defaultConstructor: () =>
          new OptionBox(0, undefined, undefined, this.userSettingsService),
      },
      'OptionBox',
      indexStorageService
    );
  }

  public async svcPromptCreateNew(
    parentId: string,
    type: OptionBoxType
  ): Promise<OptionBox> {
    const optionBox = new OptionBox(
      this.svcNextIndex(),
      parentId,
      type,
      this.userSettingsService
    );
    return optionBox;
  }

  async addOption(optionBox: OptionBox, option?: Option): Promise<Option> {
    if (!option) {
      option = await this._optionService.svcPromptCreateNew(optionBox.id);
      await this._optionService.srvAdd(option);
    }
    optionBox.optionIds.push(option.id);
    await this.svcToModify(optionBox);
    return option;
  }

  async RemoveOption(optionBox: OptionBox, optionId: string) {
    optionBox.optionIds = optionBox.optionIds.filter((id) => id !== optionId);
    await this.svcToModify(optionBox);
  }
  // also removes the options attached to the optionbox
  protected override async svcUnlink(optionBoxes: OptionBox[]) {
    let optionIds: string[] = [];
    optionBoxes.forEach((optionBox) => {
      optionIds = optionIds.concat(optionBox.optionIds);
    });
    await this._optionService.svcToRemove(optionIds);
  }

  //Método criado devido a erros no this.svcToRemove() que perde dados do Speech.
  toRemoveIdOptions(id: string) {
    this.models = this.models.filter(x => x.id !== id);
    this.toSave();
  }

  async toAddReplica(id: string, parentId: string): Promise<OptionBox> {
    const copyFrom = this.svcFindById(id);
    if (!copyFrom) {
      return null;
    }
    const pasteOn = await this.svcPromptCreateNew(parentId, copyFrom.type);
    pasteOn.label = copyFrom.label;
    await this.srvAdd(pasteOn);
    pasteOn.optionIds = [];

    const newIds: { [oldId: string]: string } = {};
    const storyProgressIds = this._storyBoxService.models.filter(x => x.id.includes(copyFrom?.id)); 

    //Localiza id dos storyProgressIds velho e devolve para o mesmo componente para evitar perda de dados.
    storyProgressIds.forEach((storyProgressOld) => {
      const speechIds = this._speechService.models.filter((x) => x.id.includes(storyProgressOld.id)).map((x) => x.id);
      const markerIds = this._markerService.models.filter((x) => x.id.includes(storyProgressOld.id)).map((x) => x.id);
      const eventIds = this._eventService.models.filter((x) => x.id.includes(storyProgressOld.id)).map((x) => x.id);
    
      const newStoryProgressIds = [...speechIds, ...markerIds, ...eventIds];
    
      if (newStoryProgressIds.length > 0) {
        storyProgressOld.storyProgressIds = newStoryProgressIds;
        this._storyBoxService.svcToModify(storyProgressOld);
      }
    });


    // Copy content
    await Promise.all(
      copyFrom.optionIds.map(async (copyFromOptionId) => {
        const replicaOption = await this._optionService.toAddReplica(copyFromOptionId, pasteOn.id);
        newIds[copyFromOptionId] = replicaOption.id;
        await this.addOption(pasteOn, replicaOption);
      })
    );

    copyFrom.optionIds.forEach((oldId, index) => {
      pasteOn.optionIds[index] = newIds[oldId];
    });

    this.svcToModify(pasteOn);

    return pasteOn;
  }

  /**
   * Gets all the Option Box objects contained in the specified Dialogue object
   * @param dialogueId The specified Dialogue ID
   * @returns The Option Box objects contained in the specified Dialogues' ID
   */
  public getByDialogue(dialogueId: string)
  {

    let result = this.models.filter(x => {
      return x.id.includes(dialogueId)
    });

    return result;

  }

    //remove da base optionboxPkg
    removeIdsOptionboxPkg(id: string) {
    let list = [];
    this.models.forEach((model) => {
    if(model.id.indexOf(id) > -1) list.push(model);
    });             
    list.forEach((x) => {
    this.svcToRemove(x.id);
    this.svcToRemoveSimpleVersion(x.id);
    }) 
   this.svcToWipeRemovedData();
   this.toSave();
  }

}
