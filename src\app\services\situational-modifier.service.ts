import { Injectable } from '@angular/core';
import { IndexStorageService, OpenAIEnvironmentService, ReviewService } from 'src/app/services';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { ModelService } from 'src/lib/darkcloud/angular/dsadmin';
import { SituationalModifier } from '../lib/@bus-tier/models';
import { ICounterItems } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { RevisorAI, RevisorObjectAI } from '../components/admin-layout/components/router/components/settings/ai-revisor/ai-revisor.component';

@Injectable({
  providedIn: 'root',
})
export class SituationalModifierService extends  ModelService<SituationalModifier> 
{

  public override svcPromptCreateNew(...args: any): Promise<SituationalModifier> {
    throw new Error('Method not implemented.');
  }

  constructor(
     private _openAIEnvironmentService: OpenAIEnvironmentService,
    indexStorageService: IndexStorageService,
    readonly userSettingsService: UserSettingsService,
    reviewService: ReviewService,
  ) 
  {
    super(
      {
        defaultConstructor: () =>
          new SituationalModifier(0, this.userSettingsService),
      },
      'SituationalModifier',
      indexStorageService,
      reviewService,
    );
  }
  
  public async createNewSubContextKnowledge() { 
    let newSubContext = new SituationalModifier(this.svcNextIndex(), this.userSettingsService); 
    this.srvAdd(newSubContext);
    return newSubContext;
  }

 // AI Revisor
  situationalModifierCounter(): ICounterItems[] {
   //Calcula as área que já foram revisadas pela AI
   const baseSize = this.models.length;
   const counterItems: ICounterItems[] = [];
   let totalItensParaRevisar = 0;
   let lengthFactor = 0;
 
   const revisedItems = this.models.reduce(
     (acc, current) => {
       for (let i = 0; i < current.factor.length; i++) {
         acc.revisedItems += (current.isReviewedFactor?.[i] ?? false) ? 1 : 0;
       }
       for (let i = 0; i < current.description.length; i++) {
         acc.revisedItems += (current.isReviewedDescription?.[i] ?? false) ? 1 : 0;
       }    
       return acc;
     },
     { revisedItems: 0 }
   );
     
     //Itens que ainda não foram revisados
   this.models.forEach((item) => {
     lengthFactor = item.factor.length;
     for (let index = 0; index < item?.factor.length; index++) {                  
       if (item.isReviewedFactor === undefined || item.isReviewedFactor[index] !== true) {
         totalItensParaRevisar++;
       }
     }
 
     for (let index = 0; index < item?.description.length; index++) {                  
       if (item.isReviewedDescription === undefined || item.isReviewedDescription[index] !== true) {
         totalItensParaRevisar++;
       }
     } 
   });        
 
       counterItems.push({
         name: 'Situational Modifier',
         baseSize: baseSize,
         totalFields: baseSize * lengthFactor, //Coluna Knowledge contém 4 linhas + suas subLinhas 11 
         totalEmptyFields: 0,
         unreviewedItems: totalItensParaRevisar,
         revisedItems: revisedItems.revisedItems,    
         suggestions: 0,
         totalNoSuggestions: 0,
         suggestionsRejected: 0,
       });
             
        return counterItems;
     }
         
 checkCounterSituationalModifier(idEnvironmentAi: string) {
       const itemsToModify: typeof this.models = [];
       const limit = this._openAIEnvironmentService.models.find(
         (x) => x.id === idEnvironmentAi
       ).limitForReview; //limite de revisão
   
       for (const item of this.models) {
         let modified = false;
 
         item.isReviewedFactor === undefined ? item.isReviewedFactor = [] : item.isReviewedFactor;
         item.isReviewedDescription === undefined ? item.isReviewedDescription = [] : item.isReviewedDescription;

 
         item.revisionCounterFactorAI === undefined ? item.revisionCounterFactorAI = [] : item.revisionCounterFactorAI;
         item.revisionCounterDescriptionAI === undefined ? item.revisionCounterDescriptionAI = [] : item.revisionCounterDescriptionAI;
        
 
       
       for (let index = 0; index < item?.factor.length; index++) {                 
         if (item?.revisionCounterFactorAI && item.revisionCounterFactorAI[index] >= limit && !item.revisionCounterFactorAI[index]) {
           item.isReviewedFactor[index] = true;
           item.revisionCounterFactorAI[index] = 0;
           modified = true;            
         }
       }
 
       for (let index = 0; index < item?.description.length; index++) {
         if (item?.revisionCounterDescriptionAI && item.revisionCounterDescriptionAI[index] >= limit && !item.isReviewedDescription[index]) {
           item.isReviewedDescription[index] = true;
           item.revisionCounterDescriptionAI[index]
           modified = true;            
         }           
       }
   
       if (modified) {
           itemsToModify.push(item);
         }
       }
   
       for (const item of itemsToModify) {
         this.svcToModify(item);
       }
   }
       
       addCounterSituationalModifier(sendList: RevisorObjectAI, suggestionList: RevisorObjectAI[], idEnvironmentAi: string) {
         const env = this._openAIEnvironmentService.models.find(
           (x) => x.id === idEnvironmentAi
         );
         if (!env) return;
     
         const limit = env.limitForReview;
         console.log('Chegou para adicionar +++', sendList);
   
           const sendItem = this.models.find((x) => x.id === sendList.id);
           
           sendItem.revisionCounterFactorAI === undefined ? sendItem.revisionCounterFactorAI = [] : sendItem.revisionCounterFactorAI;
           sendItem.revisionCounterDescriptionAI === undefined ? sendItem.revisionCounterDescriptionAI = [] : sendItem.revisionCounterDescriptionAI;

            if (suggestionList.length > 0) {// remover/separar a sugestão dos demais e fazer a contabilidade
               suggestionList.forEach((suggestion) => {
               if ((suggestion.id === sendItem.id) && suggestion?.factor.length > 0) {              
                              
                 for (let index = 0; index < sendItem?.factor.length; index++) {         
       
                   const nameCount = sendItem.revisionCounterFactorAI[index] ?? 0;
                   if (nameCount < limit) {
                     sendItem.revisionCounterFactorAI[index] = nameCount + 1;
                   } else {
                     sendItem.isReviewedFactor[index] = true;
                     sendItem.revisionCounterFactorAI[index] = 0;  
                   } 
                   if (sendItem.factor[index] === suggestion.factor) {
                     // Zera o contador do item com sugestão na posição
                      sendItem.revisionCounterFactorAI[index] = 0;  
                    }                 
                 }
               }
             });
             console.log('Suggestion List', suggestionList);
             suggestionList.forEach((suggestion) => {
               if ((sendItem.id === suggestion.id) && suggestion?.description?.length > 0) {            
                 
                 for (let index = 0; index < sendItem?.description.length; index++) {         
       
                   const nameCount = sendItem.revisionCounterDescriptionAI[index] ?? 0;
                   if (nameCount < limit) {
                     sendItem.revisionCounterDescriptionAI[index] = nameCount + 1;
                   } else {
                     sendItem.isReviewedDescription[index] = true;
                     sendItem.revisionCounterDescriptionAI[index] = 0;  
                   }
 
                   if (sendItem.description[index] === suggestion?.description) {
                     // Zera o contador do item com sugestão na posição
                      sendItem.revisionCounterDescriptionAI[index] = 0;  
                    }   
                 }       
               }
             });   
           }
           // Senão houver sugesões faz a validação aqui
           else {
             if (sendList?.factor && sendList.factor.length > 0) {
               for (let index = 0; index < sendList?.factor.length; index++) {         
       
                 const nameCount = sendItem.revisionCounterFactorAI[index] ?? 0;
                 if (nameCount < limit) {
                   sendItem.revisionCounterFactorAI[index] = nameCount + 1;
                 } else {
                   sendItem.isReviewedFactor[index] = true;
                   sendItem.revisionCounterFactorAI[index] = 0;  
                 }
               }              
             }
             
             if (sendList?.description && sendList.description.length > 0) {
               for (let index = 0; index < sendList?.description.length; index++) {         
       
                 const nameCount = sendItem.revisionCounterDescriptionAI[index] ?? 0;
                 if (nameCount < limit) {
                   sendItem.revisionCounterDescriptionAI[index] = nameCount + 1;
                 } else {
                   sendItem.isReviewedDescription[index] = true;
                   sendItem.revisionCounterDescriptionAI[index] = 0;  
                 }
               }              
             }  

           }   
 
         this.svcToModify(sendItem);
    
     }      
         //Fim do AI Revisor
}
