import { EventEmitter, Injectable } from '@angular/core';
import { Event, Marker, Speech, StoryBox } from 'src/app/lib/@bus-tier/models';
import { ModelService } from 'src/lib/darkcloud/angular/dsadmin';
import { EventService } from './event.service';
import { IndexStorageService } from './index-storage.service';
import { MarkerService } from './marker.service';
import { SpeechService } from './speech.service';
import { UserSettingsService } from './user-settings.service';

@Injectable({
  providedIn: 'root',
})
export class StoryBoxService extends ModelService<StoryBox> {
  onDelete: EventEmitter<StoryBox> = new EventEmitter<StoryBox>();

  constructor(
    private _speechService: SpeechService,
    private _eventService: EventService,
    private _markerService: MarkerService,
    indexStorageService: IndexStorageService,
    readonly userSettingsService: UserSettingsService
  ) {
    super(
      {
        defaultConstructor: () =>
          new StoryBox(0, undefined, this.userSettingsService),
      },
      'StoryBox',
      indexStorageService
    );     
  }

  public async svcPromptCreateNew(parentId: string): Promise<StoryBox> {
    let newStoryBoxAnswer = new StoryBox(this.svcNextIndex(), parentId, this.userSettingsService);
    this.srvAdd(newStoryBoxAnswer);
    return newStoryBoxAnswer;
  }

  // also removes the story progresses attached to the storybox
  protected override async svcUnlink(storyBoxes: StoryBox[]) {
    let storyProgressIds: string[] = [];
    storyBoxes.forEach((storyBox) => {
      this.onDelete.emit(storyBox);
      storyProgressIds = storyProgressIds.concat(storyBox.storyProgressIds);
    });
    await this._speechService.svcToRemove(storyProgressIds);
    await this._eventService.svcToRemove(storyProgressIds);
    await this._markerService.svcToRemove(storyProgressIds);
  }

  async addStoryProgress(storyBox: StoryBox, storyProgressId: string) {
    storyBox.storyProgressIds.push(storyProgressId);
    await this.svcToModify(storyBox);
  }

  async toRemoveStoryProgress(storyBoxId: string, storyProgressId: string) {
    const storyBox = this.svcFindById(storyBoxId);
    storyBox.storyProgressIds = storyBox.storyProgressIds.filter((id) => id !== storyProgressId);
    await this.svcToModify(storyBox);
  }

  //Método criado devido a erros no this.svcToRemove() que perde dados do Speech.
  toRemoveStory(storyBoxId: string) {
    this.models = this.models.filter((x) => x.id !== storyBoxId);
    this.toSave();
  }

  async toAddReplica(sourceId: string, parentId: string): Promise<StoryBox> {
    const copyFrom = this.svcFindById(sourceId);
    if (!copyFrom) {
      return null;
    }

    const pasteOn = await this.svcPromptCreateNew(parentId);
    pasteOn.label = copyFrom.label;
    await this.srvAdd(pasteOn);

    const newIds: { [oldId: string]: string } = {};

    await Promise.all(
      copyFrom.storyProgressIds.map(async (spId) => {
        let storyProgress: Marker | Event | Speech;
        const replicaMarker = await this.toTryAddMarkerReplica(spId, pasteOn);
        if (replicaMarker !== undefined) {
          storyProgress = replicaMarker;
        } else {
          const replicaEvent = await this.toTryAddEventReplica(spId, pasteOn);
          if (replicaEvent !== undefined) {
            storyProgress = replicaEvent;
          } else {
            const replicaSpeech = await this.toTryAddSpeechReplica(spId, pasteOn);
            storyProgress = replicaSpeech;
          }
        }
        newIds[spId] = storyProgress.id;
        await this.addStoryProgress(pasteOn, storyProgress.id);
      })
    );

    copyFrom.storyProgressIds.forEach((oldId, index) => {
      pasteOn.storyProgressIds[index] = newIds[oldId];
    });

    this.svcToModify(pasteOn);

    return pasteOn;
  }
  private async toTryAddMarkerReplica(id: string, clonedStoryBox: StoryBox): Promise<Marker> {
    const sourceMarker = this._markerService.svcFindById(id);
    if (sourceMarker !== undefined) {
      const marker = await this._markerService.toAddReplica(sourceMarker, clonedStoryBox.id);
      return marker;
    } else {
      return undefined;
    }
  }

  private async toTryAddEventReplica(id: string, clonedStoryBox: StoryBox): Promise<Event> {
    const sourceEvent = this._eventService.svcFindById(id);
    if (sourceEvent !== undefined) {
      const event = await this._eventService.toAddReplica(sourceEvent, clonedStoryBox.id);
      return event;
    } else {
      return undefined;
    }
  }

  private async toTryAddSpeechReplica(id: string, clonedStoryBox: StoryBox): Promise<Speech> {
    const sourceSpeech = this._speechService.svcFindById(id);
    if (sourceSpeech !== undefined) {
      const speech = await this._speechService.toAddReplica(sourceSpeech, clonedStoryBox.id);
      return speech;
    } else {
      return undefined;
    }
  }

  /**
   * Returns the IDs' Story Box objects contained in
   * the supplied Dialogue IDs'
   * @param ids The supplied Dialogue IDs'
   * @returns An array containin an ID of the target Story Boxes'
   */
  public filterIdsByDialogue(ids: string[]) {
    let serviceIds = this.models.map((x) => {
      return x?.id;
    });

    let validIds = ids.filter((x) => {
      return x?.includes('OB');
    });

    let answerIds: string[] = [];

    validIds.forEach((element) => {
      let newIds = serviceIds.filter((x) => {
        return x?.includes(element);
      });

      answerIds = answerIds.concat(newIds);
    });

    return answerIds;
  }

  /**
   * Gets all the Story Box objects contained in the specified Dialogue object
   * @param dialogueId The specified Dialogue ID
   * @returns The Story Box objects contained in the specified Dialogues' ID
   */
  public getByDialogue(dialogueId: string) {
    let result = this.models.filter((x) => {
      return x.id.includes(dialogueId);
    });

    return result;
  }

  //remove da base storyboxPkg
  removeIdStoryboxPkg(id: string) {
    let list = [];
    this.models.forEach((model) => {
      if (model.id.indexOf(id) > -1) list.push(model);
    });
    list.forEach((x) => {
      this.svcToRemove(x.id);
      this.svcToRemoveSimpleVersion(x.id);
    });
    this.svcToWipeRemovedData();
    this.toSave();
  }

//Método criado para que possa atualizar os storyProgressIds devido que ao remover um componente estava gerando orfãos e demovendo speech de outros componentes mais recentes.
checkSpeechInStorybox() {
  this.models.forEach((storybox) => {
    if (storybox.storyProgressIds.length === 0) {
      const speechIds = this._speechService.models.filter((speech) => speech.id.includes(storybox.id)).map((speech) => speech.id);
      if (speechIds.length > 0) {
        storybox.storyProgressIds = speechIds;
        this.svcToModify(storybox);
      }
    }
  });
}


}
