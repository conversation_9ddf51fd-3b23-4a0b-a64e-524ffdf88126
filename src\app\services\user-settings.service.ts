import { IndexStorageService } from './index-storage.service';
import { Injectable } from '@angular/core';
import { Alert } from 'src/lib/darkcloud';
import { getIconInputList } from 'src/lib/others';
import { Version } from 'src/lib/darkcloud/versioning/Version';
import { UserSettings } from 'src/app/lib/@bus-tier/models';
import { iconIds } from 'src/lib/darkcloud/angular/dsadmin/constants/iconIds';
import { PackInfo } from '../lib/interfaces';
import { IModel } from 'src/lib/darkcloud/angular/easy-mvc';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';


@Injectable({
  providedIn: 'root',
})
export class UserSettingsService implements Resolve<void> {
  static isEmpty(info: Data.Internal.Base): boolean {
    return (
      info === undefined ||
      Object.keys(info).length === 0 ||
      (!info.hex &&
        !info.authorNotes &&
        info.iconIndex === -1 &&
        !info.enablesDialogue &&
        !info.ignoreReview)
    );
  }
  public appVersion: Version = new Version(9, 10, 54);
  public appVersionString =
    'DSAdmin v' +
    this.appVersion.major +
    '.' +
    this.appVersion.minor +
    '.' +
    this.appVersion.patch;
  public buildDate = `${
    new Date().getFullYear.toString() +
    '/' +
    new Date().getMonth().toString() +
    '/' +
    new Date().getDay().toString()
  }`;

  public iconIds: any;
  public data: UserSettings;
  public lastLoadedDataInfo: {
    date: Date;
    info: PackInfo[];
  };
  public iconIndex: number;
  public getLevelModel:  IModel;
  private _typeName = 'UserSettings';
  protected _loadPromise: Promise<void>;
  public icons: { id: string; name: string }[] = [];
  public fetchInternal = this.getInformation;
  public updateInternal = (id: string, value: Data.Internal.Base) => {
    this.data.objectInformations[id] = value;
    this.toAfterChange(id);
  };

  constructor(private _indexStorageService: IndexStorageService) {
    this.toTryLoad();
  }

  async resolve(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Promise<void> {
    return this.toFinishLoading();
  }

  public async toReset() {
    await this.toFinishLoading();
    await this._indexStorageService.deleteData(this._typeName);
    await this._indexStorageService.deleteData(this._typeName, 'deleted');
    this.data = new UserSettings();
  }

  public async modify() {
    await this.SaveSettings();
  }

  public async toImportSettings(settings: UserSettings) {
    await this.toReset();
    this.data = settings;
    await this.SaveSettings();
  }

  private async toTryLoad() {
    if (this._loadPromise == undefined) {
      this._loadPromise = this.toLoad();
    }
    await this.toFinishLoading();
    this._loadPromise = undefined;
  }

  public async toFinishLoading(): Promise<void> {
    if (this._loadPromise != undefined) {
      await this._loadPromise;
    }
  }

  private async toLoad() {
    const hard =
      await this._indexStorageService.getData<Data.Hard.IUserSettings>(
        this._typeName
      );
    this.data = new UserSettings(hard) || new UserSettings();
  }

  private async SaveSettings() {
    await this._indexStorageService.setData<Data.Hard.IUserSettings>(
      this.data,
      this._typeName
    );
  }

  public getInformation(id: string): Data.Internal.Base 
  {
    if(this.data.objectInformations[id]) 
    return this.data.objectInformations[id];
    else return null
  }

  public setIconindexInformation(id: string, iconValue: number) {
    let data = this.data.objectInformations[id];
    data.iconIndex = iconValue;
    this.data.objectInformations[id] = data;
  }

  public getIcon(id: string): Data.Internal.Base {
    return this.data.icons[id] || '';
  }

  public async updateInformation<TKey extends keyof Data.Internal.Base>(
    id: string,
    key: TKey,
    value: Data.Internal.Base[TKey]
  ): Promise<void> {
    if (!this.data.objectInformations[id]) {
      this.data.objectInformations[id] = {};
    }
    const information = this.data.objectInformations[id];
    information[key] = value;

    await this.toAfterChange(id);
  }

  async toAfterChange(id: string): Promise<void> {
    const newInfo = this.data.objectInformations[id];
    if (!newInfo.hex || newInfo.hex === '#ffffff') {
      newInfo.hex = undefined;
    }
    if (!newInfo.authorNotes) {
      newInfo.authorNotes = undefined;
    }
    if (newInfo.iconIndex === -1) {
      newInfo.iconIndex = undefined;
    }
    if (!newInfo.enablesDialogue) {
      newInfo.enablesDialogue = undefined;
    }
    if (!newInfo.ignoreReview) {
      newInfo.ignoreReview = undefined;
    }

    this.data.objectInformations[id] = newInfo;
    await this.modify();
  }

  async addIcon() {
    const name = await Alert.showPrompt('Name');
    if (!name) return;
  
    const iconId = await this.selectIconId();
    if (!iconId) return;

    this.data.icons.push({ id: iconId, name });
    await this.modify();
  }

  selectIconId(): Promise<string> {
    const inputList = getIconInputList(iconIds, true);

    return Alert.showDropdown('Select Icon', inputList).then((res) => {
      if (!res) return null;

      return res;
    });
  }

  async RemoveIcon(iconId: string) {
    this.data.icons = this.data.icons.filter((o) => o.id !== iconId);
    await this.modify();
  }

  //Remove level iconIndex field
  async RemoveIconInformation(id: string) {
    if (this.data.objectInformations[id]) {    
        this.data.objectInformations[id].iconIndex;

      if(this.data.icons.length) {
        this.data.objectInformations[id].iconIndex = undefined;
      }      
      this.data.objectInformations[id].iconIndex = undefined;
      delete this.data.objectInformations[id].iconIndex
      delete this.data.objectInformations[id]["iconIndex"] 
    }
    this.getInformation(id);
    this.SaveSettings();
  }


  async changeIconId(icon: { id: string; name: string }) {
    const iconId = await this.selectIconId();
    if (!iconId) return;

    icon.id = iconId;
    await this.modify();
  } 
 //remove da base de dados
  /*removeIdObjectInformations(id: string) {       

    for(const key in this.data.objectInformations){
      if(this.data.objectInformations.hasOwnProperty(key) && key.includes(id)){
        delete this.data.objectInformations[key];
      }
    }
    this.modify();
   }*/

removeIdObjectInformations(id: string) {
  if (this.data.objectInformations[id]) {
    delete this.data.objectInformations[id];
  }
  this.modify();
}
  
}
