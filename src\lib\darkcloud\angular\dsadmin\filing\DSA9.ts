import {
  AfflictionTableService,
  AilmentTableService,
  AnimationsSelectorService,
  AnswerDilemmaBoxService,
  AreaService,
  BattleInferiorService,
  BoostIdBlockservice,
  BoostTableService,
  CategoriesXStressStatesService,
  CategoryStatusEffectService,
  CharacterService,
  CharactersSelectorService,
  ChestService,
  ClassService,
  CodeBlockDropService,
  ConditionService,
  ConfigThresholdService,
  DCGuideService,
  DCKnowledgeGuideService,
  DefensiveIdBlockservice,
  DefensiveTableService,
  DialogueService,
  AttributeDiceFrustrationService,
  DilemmaBoxService,
  DilemmaService,
  DispelIdBlockservice,
  DispelTableService,
  EffectService,
  EmotionService, EventService,
  AttributeCheckService,
  HealingIdBlockservice,
  HealingTableService,
  HybridTableService,
  IngredientDropService,
  IngredientVarianceService,
  ItemService,
  ItemsSelectorService,
  KeywordService,
  KeywordsTagsService,
  KnowledgeService,
  LevelService,
  LevelUpgradeService,
  MahankaraBehaviorTableService,
  MahankaraCategoriesService,
  MahankaraConcatenationsService,
  MahankaraGroupingsService,
  MarkerService,
  MinionStatsService,
  MissionService,
  ModifierService,
  NegativeIdBlockservice,
  ObjectiveService, OptionBoxService, OptionService,
  ParticleService,
  ParticleVarianceService,
  QnAService,
  RepetitionStatusEffectService,
  SceneryService,
  SoundService,
  SpecialSkillsService,
  SpeechService,
  StatusEffectService,
  StoryBoxService,
  StoryExpansionPkgService,
  SubContextKnowledgeService,
  SubContextService,
  ThemeService,
  TierService,
  TutorialService,
  UniqueCharactersByHCAndBLService,
  UpgradesService,
  UserSettingsService,
  VideoService,
  KnowledgeDiceFrustrationService,
  KnowledgeCheckService,
  TotalArchetypesService,
  CastsGoldenService,
  CastsSoulsService,
  CastsSigilosService,
  LevelHelperService,
  CTREVMAXService,
  CTRCollectibleService,
  LUKCollectibleService,
  INTCollectibleService,
  SPDCollectibleService,
  OpenAIEnvironmentService,
  OpenAIKeyGeneralService,
  HealingChangePerTurnPARTYService,
  HealingChangePerTurnBOOSService,
  MissionNotesService,
  ChaosIdBlockservice,
  ChaosTableService,
  RPCTableTributeAndSubmissionService,
  RPCEscapeTableService,
  MODMoonAttributesService,
  ModMoonRangesService,
  ParryPryService
} from 'src/app/services';
import { WeaponRarityService } from 'src/app/services/WeaponRarity.service';
import { AdamantiumMiningService } from 'src/app/services/adamantium-mining.service';
import { AdamantiumStorageService } from 'src/app/services/adamantium-storage.service';
import { AilmentService } from 'src/app/services/ailment.service';
import { AtributteService } from 'src/app/services/atributte.service';
import { BattleUpgradeService } from 'src/app/services/battle-upgrade.service';
import { BlueprintArchiveService } from 'src/app/services/blueprint-archive.service';
import { BonusService } from 'src/app/services/bonus.service';
import { CollectibleRarityCodeBlocksService } from 'src/app/services/collectibleRarityCodeBlocks.service';
import { CollectibleRarityGoldService } from 'src/app/services/collectibleRarityGold.service';
import { CollectibleRaritySoulsService } from 'src/app/services/collectibleRaritySouls.service';
import { CommonWeaponsService } from 'src/app/services/commonWeaponsService';
import { CustomService } from 'src/app/services/custom.service';
import { DropService } from 'src/app/services/drop.service';
import { ElementalDefensesService } from 'src/app/services/elementalDefenses.service';
import { HellniumMiningService } from 'src/app/services/hellnium-mining.service';
import { HellniumStorageService } from 'src/app/services/hellnium-storage.service';
import { ItemClassService } from 'src/app/services/item-class.service';
import { LaboratoryService } from 'src/app/services/laboratory.service';
import { LanguageService } from 'src/app/services/language.service';
import { MapsService } from 'src/app/services/maps.service';
import { MasteryService } from 'src/app/services/mastery.service';
import { MemoryModuleService } from 'src/app/services/memorymodule.service';
import { MicroloopContainerService } from 'src/app/services/microloop-container.service';
import { MicroloopService } from 'src/app/services/microloop.service';
import { MinigameService } from 'src/app/services/minigame.service';
import { NegativeTableService } from 'src/app/services/negative-table.service';
import { ParticleDropService } from 'src/app/services/particle-drop.service';
import { PassiveSkillService } from 'src/app/services/passiveSkill.service';
import { PowerUpService } from 'src/app/services/powerup.service';
import { PowerUpStatService } from 'src/app/services/powerupstat.service';
import { PrimalModifierService } from 'src/app/services/primal-modifier.service';
import { ProfanariumService } from 'src/app/services/profanarium.service';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { SilicatosService } from 'src/app/services/silicatos.service';
import { SoulsGrinderService } from 'src/app/services/souls-grinder.service';
import { SpecialWeaponService } from 'src/app/services/special-weapon.service';
import { SpecialWeaponServiceHC } from 'src/app/services/special-weaponHC.service';
import { StatusInfoService } from 'src/app/services/status-info.service';
import { StatusService } from 'src/app/services/status.service';
import { TabService } from 'src/app/services/tab.service';
import { TagService } from 'src/app/services/tag.service';
import { TitaniumMiningService } from 'src/app/services/titanium-mining.service';
import { TitaniumStorageService } from 'src/app/services/titanium-storage.service';
import { TranslationService } from 'src/app/services/translation.service';
import { WeaponService } from 'src/app/services/weapon.service';
import { WeaponUpgradeService } from 'src/app/services/weaponupgrade.service';
import { Versioning } from 'src/lib/darkcloud';
import { EasyMVC } from 'src/lib/darkcloud/angular/index';
import { AilmentDefensesService } from '../../../../../app/services/ailmentDefeneses.service';
import { ConditionTriggerService } from '../../../../../app/services/conditionTrigger.service';
import { DurationService } from '../../../../../app/services/duration.service';
import { LevelPointsService } from '../../../../../app/services/levelPoints.service';
import { PassiveAllowedService } from '../../../../../app/services/passiveAllowed.service';
import { RelicUsesService } from '../../../../../app/services/relicUses.service';
import { UniqueCharactereService } from '../../../../../app/services/uniqueCharacteres.service';
import { Data } from '../v9';
import { Assignment } from './../../../types';
import { DSA9ful } from './DSA9ful';
import { DSO9 } from './DSO9';
import { Format } from './Format';
import { PureText, pureTextParams } from './PureText';
import { SituationalModifierService } from 'src/app/services/situational-modifier.service';
import { ArchetypeListService } from 'src/app/services/archetypesList.service';
import { AmplifiersService } from 'src/app/services/amplifiers';
import { AIPromptService } from 'src/app/services/ai-prompt.service';
import { ComboTableService } from 'src/app/services/combo-table.service';


/**
 * DSADMIN v9 project file structure
 */
export class DSA9 extends Versioning.File implements Readonly<DSA9ful> {
  static assign(source: DSA9ful): DSA9 {
    const instance = new DSA9();
    Assignment.try(source, instance);
    return instance;
  }

  fileFormat: Format = 'dsa9';
  userSettings: Data.Hard.IUserSettings;
  dcGuidePkg: EasyMVC.ExportablePack<Data.Hard.IDCGuide>;
  bonusPkg: EasyMVC.ExportablePack<Data.Hard.IBonus>;
  battleInferiorPkg: EasyMVC.ExportablePack<Data.Hard.IBattleInferior>;
  keywordPkg: EasyMVC.ExportablePack<Data.Hard.IKeyword>;
  commonWeaponsPkg: EasyMVC.ExportablePack<Data.Hard.ICommonWeapons>;
  ailmentPkg: EasyMVC.ExportablePack<Data.Hard.IAilment>;
  areaPkg: EasyMVC.ExportablePack<Data.Hard.IArea>;
  atributtePkg: EasyMVC.ExportablePack<Data.Hard.IAtributte>;
  conditionTriggerPkg: EasyMVC.ExportablePack<Data.Hard.IConditionTrigger>;
  elementalDefensesPkg: EasyMVC.ExportablePack<Data.Hard.IElementalDefenses>
  ailmentDefensesPkg: EasyMVC.ExportablePack<Data.Hard.IAilmentDefenses>
  aiPromptPkg: EasyMVC.ExportablePack<Data.Hard.IAIPrompt>;
  levelPkg: EasyMVC.ExportablePack<Data.Hard.ILevel>;
  characterPkg: EasyMVC.ExportablePack<Data.Hard.ICharacter>;
  classPkg: EasyMVC.ExportablePack<Data.Hard.IClass>;
  uniqueCharacterePkg: EasyMVC.ExportablePack<Data.Hard.IUniqueCharactere>;
  levelPointsPkg: EasyMVC.ExportablePack<Data.Hard.ILevelPoints>;
  dialoguePkg: EasyMVC.ExportablePack<Data.Hard.IDialogue>;
  durationPkg: EasyMVC.ExportablePack<Data.Hard.IDuration>;
  optionboxPkg: EasyMVC.ExportablePack<Data.Hard.IOptionBox>;
  storyboxPkg: EasyMVC.ExportablePack<Data.Hard.IStoryBox>;
  optionPkg: EasyMVC.ExportablePack<Data.Hard.IOption>;
  speechPkg: EasyMVC.ExportablePack<Data.Hard.ISpeech>;
  eventPkg: EasyMVC.ExportablePack<Data.Hard.IEvent>;
  markerPkg: EasyMVC.ExportablePack<Data.Hard.IMarker>;
  emotionPkg: EasyMVC.ExportablePack<Data.Hard.IEmotion>;
  itemPkg: EasyMVC.ExportablePack<Data.Hard.IItem>;
  relicUsesPkg: EasyMVC.ExportablePack<Data.Hard.IRelicUses>;
  missionPkg: EasyMVC.ExportablePack<Data.Hard.IMission>;
  objectivePkg: EasyMVC.ExportablePack<Data.Hard.IObjective>;
  videoPkg: EasyMVC.ExportablePack<Data.Hard.IVideo>;
  themePkg: EasyMVC.ExportablePack<Data.Hard.ITheme>;
  tutorialPkg: EasyMVC.ExportablePack<Data.Hard.ITutorial>;
  sceneryPkg: EasyMVC.ExportablePack<Data.Hard.IScenery>;
  soundPkg: EasyMVC.ExportablePack<Data.Hard.ISound>;
  conditionPkg: EasyMVC.ExportablePack<Data.Hard.ICondition>;
  qnaPkg: EasyMVC.ExportablePack<Data.Hard.IQnA>;
  roadBlockPkg: EasyMVC.ExportablePack<Data.Hard.IRoadBlock>;
  minigamePkg: EasyMVC.ExportablePack<Data.Hard.IMinigame>;
  itemClassPkg: EasyMVC.ExportablePack<Data.Hard.IItemClass>;
  tagPkg: EasyMVC.ExportablePack<Data.Hard.ITag>;
  tabPkg: EasyMVC.ExportablePack<Data.Hard.ITab>;
  customPkg: EasyMVC.ExportablePack<Data.Hard.ICustom>;
  levelUpgradePkg: EasyMVC.ExportablePack<Data.Hard.ILevelUpgrade>;
  weaponPkg: EasyMVC.ExportablePack<Data.Hard.IWeapon>;
  weaponUpgradePkg: EasyMVC.ExportablePack<Data.Hard.IWeaponUpgrade>;
  particlePkg: EasyMVC.ExportablePack<Data.Hard.IParticle>;
  powerUpPkg: EasyMVC.ExportablePack<Data.Hard.IPowerUp>;
  powerUpStatPkg: EasyMVC.ExportablePack<Data.Hard.IPowerUpStat>;
  memoryModulePkg: EasyMVC.ExportablePack<Data.Hard.IMemoryModule>;
  effectPkg: EasyMVC.ExportablePack<Data.Hard.IEffect>;
  codeBlockDropPkg: EasyMVC.ExportablePack<Data.Hard.ICodeBlockDrop>;
  particleDropPkg: EasyMVC.ExportablePack<Data.Hard.IParticleDrop>;
  ingredientDropPkg: EasyMVC.ExportablePack<Data.Hard.IIngredientDrop>;
  dropPkg: EasyMVC.ExportablePack<Data.Hard.IDrop>;
  specialWeaponPkg: EasyMVC.ExportablePack<Data.Hard.ISpecialWeapon>;
  laboratoryPkg: EasyMVC.ExportablePack<Data.Hard.ILaboratory>;
  titaniumMiningPkg: EasyMVC.ExportablePack<Data.Hard.ITitaniumMining>;
  titaniumStoragePkg: EasyMVC.ExportablePack<Data.Hard.ITitaniumStorage>;
  adamantiumMiningPkg: EasyMVC.ExportablePack<Data.Hard.IAdamantiumMining>;
  adamantiumStoragePkg: EasyMVC.ExportablePack<Data.Hard.IAdamantiumStorage>;
  hellniumMiningPkg: EasyMVC.ExportablePack<Data.Hard.IHellniumMining>;
  hellniumStoragePkg: EasyMVC.ExportablePack<Data.Hard.IHellniumStorage>;
  blueprintArchivePkg: EasyMVC.ExportablePack<Data.Hard.IBlueprintArchive>;
  collectibleRarityCodeBlocksPkg: EasyMVC.ExportablePack<Data.Hard.ICollectibleRarityCodeBlocks>;
  collectibleRarityGoldPkg: EasyMVC.ExportablePack<Data.Hard.ICollectibleRarityGold>;
  collectibleRaritySoulsPkg: EasyMVC.ExportablePack<Data.Hard.ICollectibleRaritySouls>;
  profanariumPkg: EasyMVC.ExportablePack<Data.Hard.IProfanarium>;
  soulsGrinderPkg: EasyMVC.ExportablePack<Data.Hard.ISoulsGrinder>;
  statusPkg: EasyMVC.ExportablePack<Data.Hard.IStatus>;
  statusEffectPkg: EasyMVC.ExportablePack<Data.Hard.IStatusEffect>;
  tierListPkg: EasyMVC.ExportablePack<Data.Hard.ITierList>;
  masteryPkg: EasyMVC.ExportablePack<Data.Hard.IMastery>;
  primalModifierPkg: EasyMVC.ExportablePack<Data.Hard.IPrimalModifier>;
  battleUpgradePkg: EasyMVC.ExportablePack<Data.Hard.IBattleUpgrade>;
  statusInfoPkg: EasyMVC.ExportablePack<Data.Hard.IStatusInfo>;
  silicatosPkg: EasyMVC.ExportablePack<Data.Hard.ISilicatos>;
  mapsPkg: EasyMVC.ExportablePack<Data.Hard.IMaps>;
  keywordsTagsPkg: EasyMVC.ExportablePack<Data.Hard.IKeywordsTags>;
  weaponsRarityPkg: EasyMVC.ExportablePack<Data.Hard.IWeaponRarity>;
  specialWeaponsHCPkg: EasyMVC.ExportablePack<Data.Hard.ISpecialWeaponsHC>;
  itemsSelectorPkg: EasyMVC.ExportablePack<Data.Hard.IItemsSelector>;
  charactersSelectorPkg: EasyMVC.ExportablePack<Data.Hard.ICharactersSelector>;
  animationsSelectorPkg: EasyMVC.ExportablePack<Data.Hard.IAnimationsSelector>;
  storyExpansionPkg: EasyMVC.ExportablePack<Data.Hard.IStoryExpansionPkg>;
  particleVariancePkg: EasyMVC.ExportablePack<Data.Hard.IParticleVariance>;
  ingredientVariancePkg: EasyMVC.ExportablePack<Data.Hard.IIngredientVariance>;
  modifierListPkg: EasyMVC.ExportablePack<Data.Hard.IModifier>;
  chestListPkg: EasyMVC.ExportablePack<Data.Hard.IChest>;
  minionStatsPkg: EasyMVC.ExportablePack<Data.Hard.IMinionStats>;
  upgradesPkg: EasyMVC.ExportablePack<Data.Hard.IUpgrades>;
  microloopPkg: EasyMVC.ExportablePack<Data.Hard.ILevel>;
  microloopContainerPkg: EasyMVC.ExportablePack<Data.Hard.IArea>;
  translationPkg: EasyMVC.ExportablePack<Data.Hard.ITranslationPack>;
  languagePkg: EasyMVC.ExportablePack<Data.Hard.ILanguage>;
  passiveAllowedPkg: EasyMVC.ExportablePack<Data.Hard.IPassiveAllowed>;
  passiveSkillPkg: EasyMVC.ExportablePack<Data.Hard.IPassiveSkill>;
  dilemmaBoxPkg: EasyMVC.ExportablePack<Data.Hard.IDilemmaBox>;
  dilemmaPkg: EasyMVC.ExportablePack<Data.Hard.IDilemma>;
  answerDilemmaBoxPkg: EasyMVC.ExportablePack<Data.Hard.IAnswerDilemmaBox>;
  configThresholdPkg: EasyMVC.ExportablePack<Data.Hard.IConfigThreshold>;
  categoryStatusEffectPkg: EasyMVC.ExportablePack<Data.Hard.ICategory>;
  repetitionStatusEffectPkg: EasyMVC.ExportablePack<Data.Hard.IRepetition>;
  boostIdBlockPkg: EasyMVC.ExportablePack<Data.Hard.IBoostIdBlocks>;
  healingIdBlockPkg: EasyMVC.ExportablePack<Data.Hard.IHealingIdBlocks>;
  defensiveIdBlockPkg: EasyMVC.ExportablePack<Data.Hard.IDefensiveIdBlocks>;
  negativeIdBlockPkg: EasyMVC.ExportablePack<Data.Hard.INegativeIdBlocks>;
  dispelIdBlockPkg: EasyMVC.ExportablePack<Data.Hard.IDispelIdBlocks>;
  boostTablePkg: EasyMVC.ExportablePack<Data.Hard.IBoostTable>;
  healingTablePkg: EasyMVC.ExportablePack<Data.Hard.IHealingTable>;
  defensiveTablePkg: EasyMVC.ExportablePack<Data.Hard.IDefensiveTable>;
  negativeTablePkg: EasyMVC.ExportablePack<Data.Hard.INegativeTable>;
  ailmentTablePkg: EasyMVC.ExportablePack<Data.Hard.IAilmentTable>;
  dispelTablePkg: EasyMVC.ExportablePack<Data.Hard.IDispelTable>;
  hybridTablePkg: EasyMVC.ExportablePack<Data.Hard.IHybridTable>;
  afflictionTablePkg: EasyMVC.ExportablePack<Data.Hard.IAfflictionTable>;
  specialSkillsPkg: EasyMVC.ExportablePack<Data.Hard.ISpecialSkills>;
  mahankaraBehaviorTablePkg: EasyMVC.ExportablePack<Data.Hard.IMahankaraBehavior>;
  mahankaraCategoriesPkg: EasyMVC.ExportablePack<Data.Hard.IMahankaraCategories>;
  mahankaraGroupingsPkg: EasyMVC.ExportablePack<Data.Hard.IMahankaraGroupings>;
  mahankaraConcatenationsPkg: EasyMVC.ExportablePack<Data.Hard.IMahankaraConcatenations>;
  categoriesXStressStatesPkg: EasyMVC.ExportablePack<Data.Hard.ICategoriesXStressStates>;
  subContextPkg: EasyMVC.ExportablePack<Data.Hard.ISubContext>;
  attributeCheckPkg: EasyMVC.ExportablePack<Data.Hard.IAttributeCheck>;
  attributediceFrustrationPkg: EasyMVC.ExportablePack<Data.Hard.IAttributeDiceFrustration>;
  uniqueCharactersByHCAndBLPkg: EasyMVC.ExportablePack<Data.Hard.IUniqueCharactersByHCAndBL>;
  knowledgePkg: EasyMVC.ExportablePack<Data.Hard.IKnowledge>;
  subContextKnowledgePkg: EasyMVC.ExportablePack<Data.Hard.ISubContextKnowledge>;
  situationalModifierPkg: EasyMVC.ExportablePack<Data.Hard.ISituationalModifier>;
  dCKnowledgeGuidePkg: EasyMVC.ExportablePack<Data.Hard.IDCKnowledgeGuide>;
  knowledgeDiceFrustrationPkg: EasyMVC.ExportablePack<Data.Hard.IKnowledgeDiceFrustration>;
  knowledgeCheckPkg: EasyMVC.ExportablePack<Data.Hard.IKnowledgeCheck>;
  archetypeListPkg: EasyMVC.ExportablePack<Data.Hard.IArchetypeList>;
  totalArchetypesPkg: EasyMVC.ExportablePack<Data.Hard.ITotalArchetype>;
  castsGoldenPkg: EasyMVC.ExportablePack<Data.Hard.ICastsGolden>;
  castsSoulsPkg: EasyMVC.ExportablePack<Data.Hard.ICastsSouls>;
  castsSigilosPkg: EasyMVC.ExportablePack<Data.Hard.ICastsSigilos>;
  amplifiersPkg: EasyMVC.ExportablePack<Data.Hard.IAmplifiers>;
  spokePlacePkg: EasyMVC.ExportablePack<Data.Hard.ISpokePlace>;
  cTREVMAXPkg: EasyMVC.ExportablePack<Data.Hard.ICtrEvMax>;
  cTRCollectiblePkg: EasyMVC.ExportablePack<Data.Hard.ICTRCollectible>;
  lUKCollectiblePkg: EasyMVC.ExportablePack<Data.Hard.ILUKCollectible>;
  iNTCollectiblePkg: EasyMVC.ExportablePack<Data.Hard.IINTCollectible>;
  sPDCollectiblePkg: EasyMVC.ExportablePack<Data.Hard.ISPDCollectible>;
  openAIEnvironmentPkg: EasyMVC.ExportablePack<Data.Hard.IOpenAIEnvironment>;
  openAIKeyGeneraltPkg: EasyMVC.ExportablePack<Data.Hard.IOpenAIKeyGeneral>;
  healingChangePerTurnPARTYPkg: EasyMVC.ExportablePack<Data.Hard.IHealingChangePerTurnPARTY>;
  healingChangePerTurnBOOSpkg: EasyMVC.ExportablePack<Data.Hard.IHealingChangePerTurnBOOS>;
  comboTablePkg: EasyMVC.ExportablePack<Data.Hard.IComboTable>;
  missionNotesPkg: EasyMVC.ExportablePack<Data.Hard.IMissionNotes>;
  chaosIdBlockPkg: EasyMVC.ExportablePack<Data.Hard.IChaosIdBlocks>;
  chaosTablePkg: EasyMVC.ExportablePack<Data.Hard.IChaosTable>;
  rpCTableTributeAndSubmissionPkg: EasyMVC.ExportablePack<Data.Hard.IRPCTableTributeAndSubmission>;
  rpCEscapeTablePkg: EasyMVC.ExportablePack<Data.Hard.IRPCEscapeTable>;
  modMoonAttributesPkg: EasyMVC.ExportablePack<Data.Hard.IModMoonAttributes>;
  modMoonRangesPkg: EasyMVC.ExportablePack<Data.Hard.IModMoonRanges>;
  parryPryPkg: EasyMVC.ExportablePack<Data.Hard.IParryPry>;

  constructor(args?: {
    exportedDateTime: string;
    userSettingsService: UserSettingsService;
    dcGuideService: DCGuideService,
    bonusService: BonusService,
    ailmentService: AilmentService,
    ailmentDefensesService: AilmentDefensesService,
    areaService: AreaService;
    commonWeaponsService: CommonWeaponsService;
    conditionTriggerService: ConditionTriggerService,
    levelService: LevelService;
    dialogueService: DialogueService;
    durationService: DurationService,
    storyboxService: StoryBoxService;
    optionboxService: OptionBoxService;
    optionService: OptionService;
    speechService: SpeechService;
    eventService: EventService;
    markerService: MarkerService;
    characterService: CharacterService;
    classService: ClassService;
    emotionService: EmotionService;
    itemService: ItemService;
    missionService: MissionService;
    objectiveService: ObjectiveService;
    relicUsesService: RelicUsesService,
    videoService: VideoService;
    themeService: ThemeService;
    tutorialService: TutorialService;
    sceneryService: SceneryService;
    keywordService: KeywordService;
    soundService: SoundService;
    conditionService: ConditionService;
    qnaService: QnAService;
    roadBlockService: RoadBlockService;
    minigameService: MinigameService;
    itemClassService: ItemClassService;
    specialWeaponServiceHC: SpecialWeaponServiceHC;
    tagService: TagService;
    tabService: TabService;
    customService: CustomService;
    levelUpgradeService: LevelUpgradeService;
    microloopService: MicroloopService;
    microloopContainerService: MicroloopContainerService;
    translationService: TranslationService;
    languageService: LanguageService;
    weaponService: WeaponService;
    weaponUpgradeService: WeaponUpgradeService;
    particleService: ParticleService;
    powerupService: PowerUpService;
    powerupStatService: PowerUpStatService;
    memoryModuleService: MemoryModuleService;
    effectService: EffectService;
    codeBlockDropService: CodeBlockDropService;
    particleDropService: ParticleDropService;
    ingredientDropService: IngredientDropService;
    dropService: DropService;
    specialWeaponService: SpecialWeaponService;
    laboratoryService: LaboratoryService;
    titaniumMiningService: TitaniumMiningService;
    titaniumStorageService: TitaniumStorageService;
    adamantiumMiningService: AdamantiumMiningService;
    adamantiumStorageService: AdamantiumStorageService;
    hellniumMiningService: HellniumMiningService;
    hellniumStorageService: HellniumStorageService;
    blueprintArchiveService: BlueprintArchiveService;
    collectibleRarityCodeBlocksService: CollectibleRarityCodeBlocksService;
    collectibleRarityGoldService: CollectibleRarityGoldService;
    collectibleRaritySoulsService: CollectibleRaritySoulsService;
    profanariumService: ProfanariumService;
    soulsGrinderService: SoulsGrinderService;
    statusService: StatusService;
    masteryService: MasteryService;
    primalModifierService: PrimalModifierService;
    battleUpgradeService: BattleUpgradeService;
    statusInfoService: StatusInfoService;
    statusEffectService: StatusEffectService;
    tierService: TierService;
    atributte: AtributteService;
    elementalDefenses: ElementalDefensesService;
    silicatosService: SilicatosService;
    mapsService: MapsService;
    keywordTagsService: KeywordsTagsService;
    weaponsRarityService: WeaponRarityService;
    itemsSelectorService: ItemsSelectorService;
    storyExpansionPkgService: StoryExpansionPkgService;
    particleVarianceService: ParticleVarianceService;
    ingredientVarianceService: IngredientVarianceService;
    modifierService: ModifierService;
    chestService: ChestService;
    minionStatsService: MinionStatsService;
    upgradesService: UpgradesService;
    charactersSelectorService: CharactersSelectorService;
    animationsSelectorService: AnimationsSelectorService;
    passiveAllowedService: PassiveAllowedService;
    passiveSkillService: PassiveSkillService;
    uniqueCharactereService: UniqueCharactereService;
    levelPointsService: LevelPointsService;
    battleInferiorService: BattleInferiorService,
    dilemmaBoxService: DilemmaBoxService,
    dilemmaService: DilemmaService,
    answerDilemmaBoxService: AnswerDilemmaBoxService,
    configThresholdService: ConfigThresholdService,
    categoryStatusEffectService: CategoryStatusEffectService,
    repetitionStatusEffectService: RepetitionStatusEffectService,
    boostIdBlockservice: BoostIdBlockservice,
    healingIdBlockservice: HealingIdBlockservice,
    defensiveIdBlockservice: DefensiveIdBlockservice,
    negativeIdBlockservice: NegativeIdBlockservice,
    dispelIdBlockservice: DispelIdBlockservice,
    boostTableService: BoostTableService,
    healingTableService: HealingTableService,
    defensiveTableService: DefensiveTableService,
    negativeTableService: NegativeTableService,
    ailmentTableService: AilmentTableService,
    dispelTableService: DispelTableService,
    hybridTableService: HybridTableService,
    afflictionTableService: AfflictionTableService,
    specialSkillsService: SpecialSkillsService,
    mahankaraBehaviorTableService: MahankaraBehaviorTableService,
    mahankaraCategoriesService: MahankaraCategoriesService,
    mahankaraGroupingsService: MahankaraGroupingsService,
    mahankaraConcatenationsService: MahankaraConcatenationsService,
    categoriesXStressStatesService: CategoriesXStressStatesService,
    subContextService: SubContextService,
    attributeCheckService : AttributeCheckService,
    attributediceFrustrationService: AttributeDiceFrustrationService,
    uniqueCharactersByHCAndBLService: UniqueCharactersByHCAndBLService,
    knowledgeService: KnowledgeService,
    subContextKnowledgeService: SubContextKnowledgeService,
    situationalModifierService: SituationalModifierService,
    dCKnowledgeGuideService: DCKnowledgeGuideService,
    knowledgeDiceFrustrationService: KnowledgeDiceFrustrationService,
    knowledgeCheckService: KnowledgeCheckService,
    archetypeListService: ArchetypeListService,
    totalArchetypesService: TotalArchetypesService
    castsGoldenService: CastsGoldenService,
    castsSoulsService: CastsSoulsService,
    castsSigilosService: CastsSigilosService,
    amplifiersService: AmplifiersService,
    levelHelperService: LevelHelperService,
    cTREVMAXService: CTREVMAXService,
    cTRCollectibleService: CTRCollectibleService,
    lUKCollectibleService: LUKCollectibleService,
    iNTCollectibleService: INTCollectibleService,
    sPDCollectibleService: SPDCollectibleService,
    aiPromptService: AIPromptService,
    openAIEnvironmentService: OpenAIEnvironmentService,
    openAIKeyGeneralService: OpenAIKeyGeneralService,
    healingChangeService: HealingChangePerTurnPARTYService,
    healingChangeBossService: HealingChangePerTurnBOOSService,
    comboTableService: ComboTableService,
    missionNotesService: MissionNotesService,
    chaosIdBlockservice: ChaosIdBlockservice,
    chaosTableService: ChaosTableService,
    rpCTableTributeAndSubmissionService: RPCTableTributeAndSubmissionService,
    rpCEscapeTableService: RPCEscapeTableService,
    modMoonAttributesService: MODMoonAttributesService,
    modMoonRangesService: ModMoonRangesService,
    parryPryService: ParryPryService,

  }) {
    super({
      exportedDateTime: args?.exportedDateTime,
      appVersion: args?.userSettingsService?.appVersion.tostring(),
    });

    if (args != undefined) {
      this.userSettings = args.userSettingsService?.data;
      this.keywordPkg = args.keywordService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.areaPkg = args.areaService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.dcGuidePkg = args.dcGuideService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.bonusPkg = args.bonusService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.battleInferiorPkg = args.battleInferiorService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.commonWeaponsPkg = args.commonWeaponsService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.ailmentPkg = args.ailmentService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.ailmentDefensesPkg = args.ailmentDefensesService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.atributtePkg = args.atributte?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.conditionTriggerPkg = args.conditionTriggerService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.durationPkg = args.durationService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.relicUsesPkg = args.relicUsesService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.elementalDefensesPkg = args.elementalDefenses?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.levelPkg = args.levelService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.dialoguePkg = args.dialogueService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.storyboxPkg = args.storyboxService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.optionboxPkg = args.optionboxService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.optionPkg = args.optionService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.speechPkg = args.speechService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.eventPkg = args.eventService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.markerPkg = args.markerService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.characterPkg = args.characterService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.classPkg = args.classService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.emotionPkg = args.emotionService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.itemPkg = args.itemService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.missionPkg = args.missionService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.objectivePkg = args.objectiveService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.videoPkg = args.videoService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.themePkg = args.themeService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.tutorialPkg = args.tutorialService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.sceneryPkg = args.sceneryService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.soundPkg = args.soundService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.specialWeaponsHCPkg = args.specialWeaponServiceHC?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.conditionPkg = args.conditionService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.qnaPkg = args.qnaService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.roadBlockPkg = args.roadBlockService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.minigamePkg = args.minigameService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.itemClassPkg = args.itemClassService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.tagPkg = args.tagService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.tabPkg = args.tabService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.microloopPkg = args.microloopService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.microloopContainerPkg =
        args.microloopContainerService?.svcExportPack(
          this.exportedDateTime,
          this.appVersion
        );
      this.translationPkg = args.translationService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.languagePkg = args.languageService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.customPkg = args.customService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.levelUpgradePkg = args.levelUpgradeService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.weaponPkg = args.weaponService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.weaponUpgradePkg = args.weaponUpgradeService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.particlePkg = args.particleService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.powerUpPkg = args.powerupService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.powerUpStatPkg = args.powerupStatService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.memoryModulePkg = args.memoryModuleService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.effectPkg = args.effectService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.codeBlockDropPkg = args.codeBlockDropService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.ingredientDropPkg = args.ingredientDropService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.particleDropPkg = args.particleDropService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.dropPkg = args.dropService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.specialWeaponPkg = args.specialWeaponService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.laboratoryPkg = args.laboratoryService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.titaniumMiningPkg = args.titaniumMiningService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.titaniumStoragePkg = args.titaniumStorageService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.adamantiumMiningPkg = args.adamantiumMiningService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.adamantiumStoragePkg = args.adamantiumStorageService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.hellniumMiningPkg = args.hellniumMiningService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.hellniumStoragePkg = args.hellniumStorageService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.blueprintArchivePkg = args.blueprintArchiveService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.collectibleRarityCodeBlocksPkg =
        args.collectibleRarityCodeBlocksService?.svcExportPack(
          this.exportedDateTime,
          this.appVersion
        );
      this.collectibleRaritySoulsPkg =
        args.collectibleRaritySoulsService?.svcExportPack(
          this.exportedDateTime,
          this.appVersion
        );
      this.collectibleRarityGoldPkg =
        args.collectibleRarityGoldService?.svcExportPack(
          this.exportedDateTime,
          this.appVersion
        );
      this.profanariumPkg = args.profanariumService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.soulsGrinderPkg = args.soulsGrinderService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.statusPkg = args.statusService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.statusEffectPkg = args.statusEffectService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.tierListPkg = args.tierService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.masteryPkg = args.masteryService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.primalModifierPkg = args.primalModifierService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.battleUpgradePkg = args.battleUpgradeService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.statusInfoPkg = args.statusInfoService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.silicatosPkg = args.silicatosService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.mapsPkg = args.mapsService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.keywordsTagsPkg = args.keywordTagsService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.weaponsRarityPkg = args.weaponsRarityService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );

      this.itemsSelectorPkg = args.itemsSelectorService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.uniqueCharacterePkg = args.uniqueCharactereService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.levelPointsPkg = args.levelPointsService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );

      this.itemsSelectorPkg = args.itemsSelectorService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.charactersSelectorPkg = args.charactersSelectorService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.animationsSelectorPkg = args.animationsSelectorService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.storyExpansionPkg = args.storyExpansionPkgService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.particleVariancePkg = args.particleVarianceService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.ingredientVariancePkg = args.ingredientVarianceService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.modifierListPkg = args.modifierService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );

      this.chestListPkg = args.chestService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );

      this.minionStatsPkg = args.minionStatsService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );

      this.upgradesPkg = args.upgradesService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.passiveAllowedPkg = args.passiveAllowedService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.passiveSkillPkg = args.passiveSkillService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.dilemmaBoxPkg = args.dilemmaBoxService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.dilemmaPkg = args.dilemmaService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.answerDilemmaBoxPkg = args.answerDilemmaBoxService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.configThresholdPkg = args.configThresholdService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.categoryStatusEffectPkg = args.categoryStatusEffectService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.repetitionStatusEffectPkg = args.repetitionStatusEffectService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.boostIdBlockPkg = args.boostIdBlockservice?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.healingIdBlockPkg = args.healingIdBlockservice?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.defensiveIdBlockPkg = args.defensiveIdBlockservice?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.negativeIdBlockPkg = args.negativeIdBlockservice?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.dispelIdBlockPkg = args.dispelIdBlockservice?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.boostTablePkg = args.boostTableService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.healingTablePkg = args.healingTableService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.defensiveTablePkg = args.defensiveTableService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.negativeTablePkg = args.negativeTableService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.ailmentTablePkg = args.ailmentTableService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.dispelTablePkg = args.dispelTableService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.hybridTablePkg = args.hybridTableService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.afflictionTablePkg = args.afflictionTableService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.specialSkillsPkg = args.specialSkillsService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.mahankaraBehaviorTablePkg = args.mahankaraBehaviorTableService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.mahankaraCategoriesPkg = args.mahankaraCategoriesService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.mahankaraGroupingsPkg = args.mahankaraGroupingsService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.mahankaraConcatenationsPkg = args.mahankaraConcatenationsService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.categoriesXStressStatesPkg = args.categoriesXStressStatesService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.subContextPkg = args.subContextService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.attributeCheckPkg = args.attributeCheckService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.attributediceFrustrationPkg = args.attributediceFrustrationService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.uniqueCharactersByHCAndBLPkg = args.uniqueCharactersByHCAndBLService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.knowledgePkg = args.knowledgeService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.subContextKnowledgePkg = args.subContextKnowledgeService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.situationalModifierPkg = args.situationalModifierService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.dCKnowledgeGuidePkg = args.dCKnowledgeGuideService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.knowledgeDiceFrustrationPkg = args.knowledgeDiceFrustrationService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.knowledgeCheckPkg = args.knowledgeCheckService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.archetypeListPkg = args.archetypeListService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.totalArchetypesPkg = args.totalArchetypesService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.castsGoldenPkg = args.castsGoldenService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.castsSoulsPkg = args.castsSoulsService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.castsSigilosPkg = args.castsSigilosService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.amplifiersPkg = args.amplifiersService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.spokePlacePkg = args.levelHelperService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.cTREVMAXPkg = args.cTREVMAXService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.cTRCollectiblePkg = args.cTRCollectibleService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.lUKCollectiblePkg = args.lUKCollectibleService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.iNTCollectiblePkg = args.iNTCollectibleService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.sPDCollectiblePkg = args.sPDCollectibleService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.aiPromptPkg = args.aiPromptService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.openAIEnvironmentPkg = args.openAIEnvironmentService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.openAIKeyGeneraltPkg = args.openAIKeyGeneralService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      ); 
      this.healingChangePerTurnPARTYPkg = args.healingChangeService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.healingChangePerTurnBOOSpkg = args.healingChangeBossService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.comboTablePkg = args.comboTableService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.missionNotesPkg = args.missionNotesService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.chaosIdBlockPkg = args.chaosIdBlockservice?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.chaosTablePkg = args.chaosTableService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.rpCTableTributeAndSubmissionPkg = args.rpCTableTributeAndSubmissionService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.rpCEscapeTablePkg = args.rpCEscapeTableService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.modMoonAttributesPkg = args.modMoonAttributesService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.modMoonRangesPkg = args.modMoonRangesService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
      this.parryPryPkg = args.parryPryService?.svcExportPack(
        this.exportedDateTime,
        this.appVersion
      );
     
    }
  }
  /**
   * prepares a DSO9 file and returns it
   */
  exportOrthography(): DSO9 {
    return new DSO9(
      this.exportedDateTime,
      this.appVersion,
      this.ExportTextPackages(this.areaPkg),
      this.ExportTextPackages(this.ailmentPkg),
      this.ExportTextPackages(this.dcGuidePkg),
      this.ExportTextPackages(this.bonusPkg),
      this.ExportTextPackages(this.battleInferiorPkg),
      this.ExportTextPackages(this.ailmentDefensesPkg),
      this.ExportTextPackages(this.commonWeaponsPkg),
      this.ExportTextPackages(this.atributtePkg),
      this.ExportTextPackages(this.conditionTriggerPkg),
      this.ExportTextPackages(this.durationPkg),
      this.ExportTextPackages(this.elementalDefensesPkg),
      this.ExportTextPackages(this.levelPkg),
      this.ExportTextPackages(this.characterPkg),
      this.ExportTextPackages(this.classPkg),
      this.ExportTextPackages(this.optionPkg),
      this.ExportTextPackages(this.speechPkg),
      this.ExportTextPackages(this.relicUsesPkg),
      this.ExportTextPackages(this.emotionPkg),
      this.ExportTextPackages(this.itemPkg),
      this.ExportTextPackages(this.missionPkg),
      this.ExportTextPackages(this.objectivePkg),
      this.ExportTextPackages(this.videoPkg),
      this.ExportTextPackages(this.themePkg),
      this.ExportTextPackages(this.tutorialPkg),
      this.ExportTextPackages(this.sceneryPkg),
      this.ExportTextPackages(this.weaponPkg),
      this.ExportTextPackages(this.weaponUpgradePkg),
      this.ExportTextPackages(this.particlePkg),
      this.ExportTextPackages(this.powerUpPkg),
      this.ExportTextPackages(this.powerUpStatPkg),
      this.ExportTextPackages(this.memoryModulePkg),
      this.ExportTextPackages(this.effectPkg),
      this.ExportTextPackages(this.tagPkg),
      this.ExportTextPackages(this.tabPkg),
      this.ExportTextPackages(this.tabPkg),
      this.ExportTextPackages(this.codeBlockDropPkg),
      this.ExportTextPackages(this.ingredientDropPkg),
      this.ExportTextPackages(this.particleDropPkg),
      this.ExportTextPackages(this.dropPkg),
      this.ExportTextPackages(this.specialWeaponPkg),
      this.ExportTextPackages(this.laboratoryPkg),
      this.ExportTextPackages(this.titaniumMiningPkg),
      this.ExportTextPackages(this.titaniumStoragePkg),
      this.ExportTextPackages(this.adamantiumMiningPkg),
      this.ExportTextPackages(this.adamantiumStoragePkg),
      this.ExportTextPackages(this.hellniumMiningPkg),
      this.ExportTextPackages(this.hellniumStoragePkg),
      this.ExportTextPackages(this.blueprintArchivePkg),
      this.ExportTextPackages(this.collectibleRarityCodeBlocksPkg),
      this.ExportTextPackages(this.collectibleRarityGoldPkg),
      this.ExportTextPackages(this.collectibleRaritySoulsPkg),
      this.ExportTextPackages(this.profanariumPkg),
      this.ExportTextPackages(this.soulsGrinderPkg),
      this.ExportTextPackages(this.statusPkg),
      this.ExportTextPackages(this.statusEffectPkg),
      this.ExportTextPackages(this.specialWeaponsHCPkg),
      this.ExportTextPackages(this.tierListPkg),
      this.ExportTextPackages(this.masteryPkg),
      this.ExportTextPackages(this.uniqueCharacterePkg),
      this.ExportTextPackages(this.levelPointsPkg),
      this.ExportTextPackages(this.primalModifierPkg),
      this.ExportTextPackages(this.battleUpgradePkg),
      this.ExportTextPackages(this.statusInfoPkg),
      this.ExportTextPackages(this.silicatosPkg),
      this.ExportTextPackages(this.mapsPkg),
      this.ExportTextPackages(this.charactersSelectorPkg),
      this.ExportTextPackages(this.animationsSelectorPkg),
      this.ExportTextPackages(this.storyExpansionPkg),
      this.ExportTextPackages(this.particleVariancePkg),
      this.ExportTextPackages(this.modifierListPkg),
      this.ExportTextPackages(this.chestListPkg),
      this.ExportTextPackages(this.minionStatsPkg),
      this.ExportTextPackages(this.upgradesPkg),
      this.ExportTextPackages(this.itemsSelectorPkg),
      this.ExportTextPackages(this.passiveAllowedPkg),
      this.ExportTextPackages(this.passiveSkillPkg),
      this.ExportTextPackages(this.dilemmaBoxPkg),
      this.ExportTextPackages(this.dilemmaPkg),
      this.ExportTextPackages(this.answerDilemmaBoxPkg),
      this.ExportTextPackages(this.configThresholdPkg),
      this.ExportTextPackages(this.categoryStatusEffectPkg),
      this.ExportTextPackages(this.repetitionStatusEffectPkg),
      this.ExportTextPackages(this.boostIdBlockPkg),
      this.ExportTextPackages(this.healingIdBlockPkg),
      this.ExportTextPackages(this.defensiveIdBlockPkg),
      this.ExportTextPackages(this.negativeIdBlockPkg),
      this.ExportTextPackages(this.dispelIdBlockPkg),
      this.ExportTextPackages(this.boostTablePkg),
      this.ExportTextPackages(this.healingTablePkg),
      this.ExportTextPackages(this.defensiveTablePkg),
      this.ExportTextPackages(this.negativeTablePkg),
      this.ExportTextPackages(this.ailmentTablePkg),
      this.ExportTextPackages(this.dispelTablePkg),
      this.ExportTextPackages(this.hybridTablePkg),
      this.ExportTextPackages(this.afflictionTablePkg),
      this.ExportTextPackages(this.specialSkillsPkg),
      this.ExportTextPackages(this.mahankaraBehaviorTablePkg),
      this.ExportTextPackages(this.mahankaraCategoriesPkg),
      this.ExportTextPackages(this.mahankaraGroupingsPkg),
      this.ExportTextPackages(this.mahankaraConcatenationsPkg),
      this.ExportTextPackages(this.categoriesXStressStatesPkg),
      this.ExportTextPackages(this.subContextPkg),
      this.ExportTextPackages(this.attributeCheckPkg),
      this.ExportTextPackages(this.attributediceFrustrationPkg),
      this.ExportTextPackages(this.uniqueCharactersByHCAndBLPkg),
      this.ExportTextPackages(this.knowledgePkg),
      this.ExportTextPackages(this.subContextKnowledgePkg),
      this.ExportTextPackages(this.situationalModifierPkg),
      this.ExportTextPackages(this.dCKnowledgeGuidePkg),
      this.ExportTextPackages(this.knowledgeDiceFrustrationPkg),
      this.ExportTextPackages(this.knowledgeCheckPkg),
      this.ExportTextPackages(this.archetypeListPkg),
      this.ExportTextPackages(this.totalArchetypesPkg),
      this.ExportTextPackages(this.castsGoldenPkg),
      this.ExportTextPackages(this.castsSoulsPkg),
      this.ExportTextPackages(this.castsSigilosPkg),
      this.ExportTextPackages(this.amplifiersPkg),
      this.ExportTextPackages(this.spokePlacePkg),
      this.ExportTextPackages(this.cTREVMAXPkg),
      this.ExportTextPackages(this.cTRCollectiblePkg),
      this.ExportTextPackages(this.lUKCollectiblePkg),
      this.ExportTextPackages(this.iNTCollectiblePkg),
      this.ExportTextPackages(this.sPDCollectiblePkg),
      this.ExportTextPackages(this.aiPromptPkg),
      this.ExportTextPackages(this.openAIEnvironmentPkg),
      this.ExportTextPackages(this.openAIKeyGeneraltPkg),
      this.ExportTextPackages(this.healingChangePerTurnPARTYPkg),
      this.ExportTextPackages(this.healingChangePerTurnBOOSpkg),
      this.ExportTextPackages(this.comboTablePkg),
      this.ExportTextPackages(this.missionNotesPkg),
      this.ExportTextPackages(this.chaosIdBlockPkg),
      this.ExportTextPackages(this.chaosTablePkg),
      this.ExportTextPackages(this.rpCTableTributeAndSubmissionPkg),
      this.ExportTextPackages(this.rpCEscapeTablePkg),
      this.ExportTextPackages(this.modMoonAttributesPkg),
      this.ExportTextPackages(this.modMoonRangesPkg),
    );
  }

  /**
   * imports a DSO9 file and returns the log in HTML format
   */
  importOrthography(dso: DSO9): string {
    let html =
      '<table class="table" style="text-align: left"><thead><th>Before</th><th>After</th></thead><tbody>';
    html += this.ImportTextPackages(this.ailmentPkg, dso.ailmentPkg);
    html += this.ImportTextPackages(this.ailmentDefensesPkg, dso.ailmentDefensesPkg);
    html += this.ImportTextPackages(this.dcGuidePkg, dso.dcGuidePkg);
    html += this.ImportTextPackages(this.bonusPkg, dso.bonusPkg);
    html += this.ImportTextPackages(this.battleInferiorPkg, dso.battleInferiorPkg);
    html += this.ImportTextPackages(this.areaPkg, dso.areaPkg);
    html += this.ImportTextPackages(this.commonWeaponsPkg, dso.commonWeaponsPkg);
    html += this.ImportTextPackages(this.atributtePkg, dso.atributtePkg);
    html += this.ImportTextPackages(this.conditionTriggerPkg, dso.conditionTriggerPkg);
    html += this.ImportTextPackages(this.durationPkg, dso.durationPkg);
    html += this.ImportTextPackages(this.elementalDefensesPkg, dso.elementalDefensesPkg);
    html += this.ImportTextPackages(this.levelPkg, dso.levelPkg);
    html += this.ImportTextPackages(this.characterPkg, dso.characterPkg);
    html += this.ImportTextPackages(this.relicUsesPkg, dso.relicUsesPkg);
    html += this.ImportTextPackages(this.classPkg, dso.classPkg);
    html += this.ImportTextPackages(this.optionPkg, dso.optionPkg);
    html += this.ImportTextPackages(this.speechPkg, dso.storyProgress_speechPkg);
    html += this.ImportTextPackages(this.emotionPkg, dso.emotionPkg);
    html += this.ImportTextPackages(this.itemPkg, dso.itemPkg);
    html += this.ImportTextPackages(this.missionPkg, dso.missionPkg);
    html += this.ImportTextPackages(this.objectivePkg, dso.objectivePkg);
    html += this.ImportTextPackages(this.videoPkg, dso.videoPkg);
    html += this.ImportTextPackages(this.themePkg, dso.videoThemePkg);
    html += this.ImportTextPackages(this.tutorialPkg, dso.tutorialPkg);
    html += this.ImportTextPackages(this.sceneryPkg, dso.sceneryPkg);
    html += this.ImportTextPackages(this.specialWeaponsHCPkg, dso.specialWeaponsHCPkg);
    html += this.ImportTextPackages(this.weaponPkg, dso.weaponPkg);
    html += this.ImportTextPackages(this.weaponUpgradePkg, dso.weaponUpgradePkg);
    html += this.ImportTextPackages(this.particlePkg, dso.particlePkg);
    html += this.ImportTextPackages(this.powerUpPkg, dso.powerUpPkg);
    html += this.ImportTextPackages(this.powerUpStatPkg, dso.powerUpStatPkg);
    html += this.ImportTextPackages(this.memoryModulePkg, dso.memoryModulePkg);
    html += this.ImportTextPackages(this.effectPkg, dso.effectPkg);
    html += this.ImportTextPackages(this.tagPkg, dso.tagPkg);
    html += this.ImportTextPackages(this.tabPkg, dso.tabPkg);
    html += this.ImportTextPackages(this.tabPkg, dso.tabPkg);
    html += this.ImportTextPackages(this.codeBlockDropPkg, dso.codeBlockDropPkg);
    html += this.ImportTextPackages(this.particleDropPkg, dso.particleDropPkg);
    html += this.ImportTextPackages(this.ingredientDropPkg, dso.ingredientDropPkg);
    html += this.ImportTextPackages(this.dropPkg, dso.dropPkg);
    html += this.ImportTextPackages(this.specialWeaponPkg, dso.specialWeaponPkg);
    html += this.ImportTextPackages(this.laboratoryPkg, dso.laboratoryPkg);
    html += this.ImportTextPackages(this.uniqueCharacterePkg, dso.uniqueCharacterePkg);
    html += this.ImportTextPackages(this.levelPointsPkg, dso.levelPointsPkg)

    html += this.ImportTextPackages(
      this.titaniumMiningPkg,
      dso.titaniumMiningPkg
    );
    html += this.ImportTextPackages(
      this.titaniumStoragePkg,
      dso.titaniumStoragePkg
    );
    html += this.ImportTextPackages(
      this.adamantiumMiningPkg,
      dso.adamantiumMiningPkg
    );
    html += this.ImportTextPackages(
      this.adamantiumStoragePkg,
      dso.adamantiumStoragePkg
    );
    html += this.ImportTextPackages(
      this.hellniumMiningPkg,
      dso.hellniumMiningPkg
    );
    html += this.ImportTextPackages(
      this.hellniumStoragePkg,
      dso.hellniumStoragePkg
    );
    html += this.ImportTextPackages(
      this.blueprintArchivePkg,
      dso.blueprintArchivePkg
    );
    html += this.ImportTextPackages(
      this.collectibleRarityCodeBlocksPkg,
      dso.collectibleRarityCodeBlocksPkg
    );
    html += this.ImportTextPackages(
      this.collectibleRarityGoldPkg,
      dso.collectibleRarityGoldPkg
    );
    html += this.ImportTextPackages(
      this.collectibleRaritySoulsPkg,
      dso.collectibleRaritySoulsPkg
    );
    html += this.ImportTextPackages(this.profanariumPkg, dso.profanariumPkg);
    html += this.ImportTextPackages(this.soulsGrinderPkg, dso.soulsGrinderPkg);
    html += this.ImportTextPackages(this.statusPkg, dso.statusPkg);
    html += this.ImportTextPackages(this.statusEffectPkg, dso.statusEffectPkg);
    html += this.ImportTextPackages(this.tierListPkg, dso.tierListPkg);
    html += this.ImportTextPackages(this.masteryPkg, dso.masteryPkg);
    html += this.ImportTextPackages(
      this.primalModifierPkg,
      dso.primalModifierPkg
    );
    html += this.ImportTextPackages(
      this.battleUpgradePkg,
      dso.battleUpgradePkg
    );
    html += this.ImportTextPackages(this.statusInfoPkg, dso.statusInfoPkg);
    html += this.ImportTextPackages(this.silicatosPkg, dso.silicatosPkg);
    html += this.ImportTextPackages(this.mapsPkg, dso.mapsPkg);
    html += this.ImportTextPackages(this.charactersSelectorPkg, dso.charactersSelectorPkg);
    html += this.ImportTextPackages(this.animationsSelectorPkg, dso.animationsSelectorPkg);
    html += this.ImportTextPackages(this.itemsSelectorPkg, dso.itemsSelectorPkg);
    html += this.ImportTextPackages(this.passiveAllowedPkg, dso.passiveAllowedPkg);
    html += this.ImportTextPackages(this.passiveSkillPkg, dso.passiveSkillPkg);
    html += this.ImportTextPackages(this.dilemmaBoxPkg, dso.dilemmaBoxPkg);
    html += this.ImportTextPackages(this.dilemmaPkg, dso.dilemmaPkg);
    html += this.ImportTextPackages(this.answerDilemmaBoxPkg, dso.answerDilemmaBoxPkg);
    html += this.ImportTextPackages(this.configThresholdPkg, dso.configThresholdPkg);
    html += this.ImportTextPackages(this.categoryStatusEffectPkg, dso.categoryStatusEffectPkg);
    html += this.ImportTextPackages(this.repetitionStatusEffectPkg, dso.repetitionStatusEffectPkg);
    html += this.ImportTextPackages(this.boostIdBlockPkg, dso.boostIdBlockPkg);
    html += this.ImportTextPackages(this.healingIdBlockPkg, dso.healingIdBlockPkg);
    html += this.ImportTextPackages(this.defensiveIdBlockPkg, dso.defensiveIdBlockPkg);
    html += this.ImportTextPackages(this.negativeIdBlockPkg, dso.negativeIdBlockPkg);
    html += this.ImportTextPackages(this.dispelIdBlockPkg, dso.dispelIdBlockPkg);
    html += this.ImportTextPackages(this.boostTablePkg, dso.boostTablePkg);
    html += this.ImportTextPackages(this.healingTablePkg, dso.healingTablePkg);
    html += this.ImportTextPackages(this.defensiveTablePkg, dso.defensiveTablePkg);
    html += this.ImportTextPackages(this.negativeTablePkg, dso.negativeTablePkg);
    html += this.ImportTextPackages(this.ailmentTablePkg, dso.ailmentTablePkg);
    html += this.ImportTextPackages(this.dispelTablePkg, dso.dispelTablePkg);
    html += this.ImportTextPackages(this.hybridTablePkg, dso.hybridTablePkg);
    html += this.ImportTextPackages(this.afflictionTablePkg, dso.afflictionTablePkg);
    html += this.ImportTextPackages(this.specialSkillsPkg, dso.specialSkillsPkg);
    html += this.ImportTextPackages(this.mahankaraBehaviorTablePkg, dso.mahankaraBehaviorTablePkg);
    html += this.ImportTextPackages(this.mahankaraCategoriesPkg, dso.mahankaraCategoriesPkg);
    html += this.ImportTextPackages(this.mahankaraGroupingsPkg, dso.mahankaraGroupingsPkg);
    html += this.ImportTextPackages(this.mahankaraConcatenationsPkg, dso.mahankaraConcatenationsPkg);
    html += this.ImportTextPackages(this.categoriesXStressStatesPkg, dso.categoriesXStressStatesPkg);
    html += this.ImportTextPackages(this.subContextPkg, dso.subContextPkg);
    html += this.ImportTextPackages(this.attributeCheckPkg, dso.attributeCheckPkg);
    html += this.ImportTextPackages(this.attributediceFrustrationPkg, dso.attributediceFrustrationPkg);
    html += this.ImportTextPackages(this.uniqueCharactersByHCAndBLPkg, dso.uniqueCharactersByHCAndBLPkg);
    html += this.ImportTextPackages(this.knowledgePkg, dso.knowledgePkg);
    html += this.ImportTextPackages(this.subContextKnowledgePkg, dso.subContextKnowledgePkg);
    html += this.ImportTextPackages(this.situationalModifierPkg, dso.situationalModifierPkg);
    html += this.ImportTextPackages(this.dCKnowledgeGuidePkg, dso.dCKnowledgeGuidePkg);
    html += this.ImportTextPackages(this.knowledgeDiceFrustrationPkg, dso.knowledgeDiceFrustrationPkg);
    html += this.ImportTextPackages(this.knowledgeCheckPkg, dso.knowledgeCheckPkg);
    html += this.ImportTextPackages(this.archetypeListPkg, dso.archetypeListPkg);
    html += this.ImportTextPackages(this.totalArchetypesPkg, dso.totalArchetypesPkg);
    html += this.ImportTextPackages(this.castsGoldenPkg, dso.castsGoldenPkg);
    html += this.ImportTextPackages(this.castsSoulsPkg, dso.castsSoulsPkg);
    html += this.ImportTextPackages(this.castsSigilosPkg, dso.castsSigilosPkg);
    html += this.ImportTextPackages(this.amplifiersPkg, dso.amplifiersPkg);
    html += this.ImportTextPackages(this.spokePlacePkg, dso.spokePlacePkg);
    html += this.ImportTextPackages(this.cTREVMAXPkg, dso.cTREVMAXPkg);
    html += this.ImportTextPackages(this.cTRCollectiblePkg, dso.cTRCollectiblePkg);
    html += this.ImportTextPackages(this.lUKCollectiblePkg, dso.lUKCollectiblePkg);
    html += this.ImportTextPackages(this.iNTCollectiblePkg, dso.iNTCollectiblePkg);
    html += this.ImportTextPackages(this.sPDCollectiblePkg, dso.sPDCollectiblePkg);
    html += this.ImportTextPackages(this.aiPromptPkg, dso.aiPromptPkg);
    html += this.ImportTextPackages(this.openAIEnvironmentPkg, dso.openAIEnvironmentPkg);
    html += this.ImportTextPackages(this.openAIKeyGeneraltPkg, dso.openAIKeyGeneraltPkg);
    html += this.ImportTextPackages(this.healingChangePerTurnPARTYPkg, dso.healingChangePerTurnPARTYPkg);
    html += this.ImportTextPackages(this.healingChangePerTurnBOOSpkg, dso.healingChangePerTurnBOOSpkg);
    html += this.ImportTextPackages(this.comboTablePkg, dso.comboTablePkg);
    html += this.ImportTextPackages(this.missionNotesPkg, dso.missionNotesPkg);
    html += this.ImportTextPackages(this.chaosIdBlockPkg, dso.chaosIdBlockPkg);
    html += this.ImportTextPackages(this.chaosTablePkg, dso.chaosTablePkg);
    html += this.ImportTextPackages(this.rpCTableTributeAndSubmissionPkg, dso.rpCTableTributeAndSubmissionPkg);
    html += this.ImportTextPackages(this.rpCEscapeTablePkg, dso.rpCEscapeTablePkg);
    html += this.ImportTextPackages(this.modMoonAttributesPkg, dso.modMoonAttributesPkg);
    html += this.ImportTextPackages(this.modMoonRangesPkg, dso.modMoonRangesPkg);
    html += this.ImportTextPackages(this.parryPryPkg, dso.parryPryPkg);
    html += '</tbody></table>';
    return html;
  }

  /**
   * imports text packages from a PureText exportable package
   */
  private ImportTextPackages<E extends EasyMVC.ExportablePack<unknown>>(
    pkg: E,
    textPkg: EasyMVC.ExportablePack<PureText>
  ): string {
    let html = '';
    pkg.data.forEach((obj, i) => {
      pureTextParams.forEach((param) => {
        if (textPkg.data[i][param] !== undefined) {
          if (obj[param] !== textPkg.data[i][param]) {
            html +=
              '<tr><td>' +
              obj[param] +
              '</td><td>' +
              textPkg.data[i][param] +
              '</td></tr>';
          }
          obj[param] = textPkg.data[i][param];
        }
      });
    });
    return html;
  }

  /**
   * returns a PureText exportable package
   */
  private ExportTextPackages(
    pkg: EasyMVC.ExportablePack<unknown>
  ): EasyMVC.ExportablePack<PureText> {
    const textPkg = new EasyMVC.ExportablePack<PureText>();
    pkg.data.forEach((obj, i) => {
      textPkg.data[i] = new PureText();
      pureTextParams.forEach((param) => {
        if (obj[param] !== undefined) {
          textPkg.data[i][param] = obj[param];
        }
      });
    });
    return textPkg;
  }
}
