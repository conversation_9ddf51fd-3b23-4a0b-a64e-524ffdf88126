import { Versioning } from 'src/lib/darkcloud';
import { EasyMVC } from 'src/lib/darkcloud/angular';
import { Format } from './Format';
import { PureText } from './PureText';

/**
 * DSADMIN v8 file structure to export for orthrographic correction.
 */
export class DSO9 extends Versioning.File {
  constructor(
    exportedDateTime?: string,
    appVersion?: string,
    public ailmentPkg?: EasyMVC.ExportablePack<PureText>,
    public areaPkg?: EasyMVC.ExportablePack<PureText>,
    public dcGuidePkg?: EasyMVC.ExportablePack<PureText>,
    public atributtePkg?: EasyMVC.ExportablePack<PureText>,
    public bonusPkg?: EasyMVC.ExportablePack<PureText>,
    public battleInferiorPkg?: EasyMVC.ExportablePack<PureText>,
    public commonWeaponsPkg?: EasyMVC.ExportablePack<PureText>,
    public durationPkg?: EasyMVC.ExportablePack<PureText>,
    public elementalDefensesPkg?: EasyMVC.ExportablePack<PureText>,
    public ailmentDefensesPkg?: EasyMVC.ExportablePack<PureText>,
    public conditionTriggerPkg?: EasyMVC.ExportablePack<PureText>,
    public levelPkg?: EasyMVC.ExportablePack<PureText>,
    public characterPkg?: EasyMVC.ExportablePack<PureText>,
    public classPkg?: EasyMVC.ExportablePack<PureText>,
    public optionPkg?: EasyMVC.ExportablePack<PureText>,
    public storyProgress_speechPkg?: EasyMVC.ExportablePack<PureText>,
    public emotionPkg?: EasyMVC.ExportablePack<PureText>,
    public itemPkg?: EasyMVC.ExportablePack<PureText>,
    public missionPkg?: EasyMVC.ExportablePack<PureText>,
    public objectivePkg?: EasyMVC.ExportablePack<PureText>,
    public videoPkg?: EasyMVC.ExportablePack<PureText>,
    public videoMessagePkg?: EasyMVC.ExportablePack<PureText>,
    public videoThemePkg?: EasyMVC.ExportablePack<PureText>,
    public tutorialPkg?: EasyMVC.ExportablePack<PureText>,
    public sceneryPkg?: EasyMVC.ExportablePack<PureText>,
    public weaponPkg?: EasyMVC.ExportablePack<PureText>,
    public weaponUpgradePkg?: EasyMVC.ExportablePack<PureText>,
    public particlePkg?: EasyMVC.ExportablePack<PureText>,
    public powerUpPkg?: EasyMVC.ExportablePack<PureText>,
    public powerUpStatPkg?: EasyMVC.ExportablePack<PureText>,
    public memoryModulePkg?: EasyMVC.ExportablePack<PureText>,
    public effectPkg?: EasyMVC.ExportablePack<PureText>,
    public tagPkg?: EasyMVC.ExportablePack<PureText>,
    public tabPkg?: EasyMVC.ExportablePack<PureText>,
    public codeBlockDropPkg?: EasyMVC.ExportablePack<PureText>,
    public particleDropPkg?: EasyMVC.ExportablePack<PureText>,
    public ingredientDropPkg?: EasyMVC.ExportablePack<PureText>,
    public dropPkg?: EasyMVC.ExportablePack<PureText>,
    public specialWeaponPkg?: EasyMVC.ExportablePack<PureText>,
    public laboratoryPkg?: EasyMVC.ExportablePack<PureText>,
    public titaniumMiningPkg?: EasyMVC.ExportablePack<PureText>,
    public titaniumStoragePkg?: EasyMVC.ExportablePack<PureText>,
    public adamantiumMiningPkg?: EasyMVC.ExportablePack<PureText>,
    public adamantiumStoragePkg?: EasyMVC.ExportablePack<PureText>,
    public hellniumMiningPkg?: EasyMVC.ExportablePack<PureText>,
    public hellniumStoragePkg?: EasyMVC.ExportablePack<PureText>,
    public blueprintArchivePkg?: EasyMVC.ExportablePack<PureText>,
    public collectibleRaritySoulsPkg?: EasyMVC.ExportablePack<PureText>,
    public collectibleRarityGoldPkg?: EasyMVC.ExportablePack<PureText>,
    public collectibleRarityCodeBlocksPkg?: EasyMVC.ExportablePack<PureText>,
    public profanariumPkg?: EasyMVC.ExportablePack<PureText>,
    public soulsGrinderPkg?: EasyMVC.ExportablePack<PureText>,
    public statusPkg?: EasyMVC.ExportablePack<PureText>,
    public statusEffectPkg?: EasyMVC.ExportablePack<PureText>,
    public tierListPkg?: EasyMVC.ExportablePack<PureText>,
    public masteryPkg?: EasyMVC.ExportablePack<PureText>,
    public primalModifierPkg?: EasyMVC.ExportablePack<PureText>,
    public battleUpgradePkg?: EasyMVC.ExportablePack<PureText>,
    public statusInfoPkg?: EasyMVC.ExportablePack<PureText>,
    public silicatosPkg?: EasyMVC.ExportablePack<PureText>,
    public mapsPkg?: EasyMVC.ExportablePack<PureText>,
    public charactersSelectorPkg?: EasyMVC.ExportablePack<PureText>,
    public itemsSelectorPkg?: EasyMVC.ExportablePack<PureText>,
    public animationsSelectorPkg?: EasyMVC.ExportablePack<PureText>,
    public storyExpansionPkg?: EasyMVC.ExportablePack<PureText>,
    public particleVariancePkg?: EasyMVC.ExportablePack<PureText>,
    public ingredientVariancePkg?: EasyMVC.ExportablePack<PureText>,
    public modifierListPkg?: EasyMVC.ExportablePack<PureText>,
    public chestListPkg?: EasyMVC.ExportablePack<PureText>,
    public minionStatsPkg?: EasyMVC.ExportablePack<PureText>,
    public upgradesPkg?: EasyMVC.ExportablePack<PureText>,
    public specialWeaponsHCPkg?: EasyMVC.ExportablePack<PureText>,
    public passiveAllowedPkg?: EasyMVC.ExportablePack<PureText>,
    public passiveSkillPkg?: EasyMVC.ExportablePack<PureText>,
    public inspirationPointRarityPkg?: EasyMVC.ExportablePack<PureText>,
    public uniqueCharacterePkg?: EasyMVC.ExportablePack<PureText>,
    public levelPointsPkg?: EasyMVC.ExportablePack<PureText>,
    public relicUsesPkg?: EasyMVC.ExportablePack<PureText>,
    public dilemmaBoxPkg?: EasyMVC.ExportablePack<PureText>,
    public dilemmaPkg?: EasyMVC.ExportablePack<PureText>,
    public answerDilemmaBoxPkg?: EasyMVC.ExportablePack<PureText>,
    public configThresholdPkg?: EasyMVC.ExportablePack<PureText>,
    public categoryStatusEffectPkg?: EasyMVC.ExportablePack<PureText>,
    public repetitionStatusEffectPkg?: EasyMVC.ExportablePack<PureText>,
    public boostIdBlockPkg?: EasyMVC.ExportablePack<PureText>,
    public healingIdBlockPkg?: EasyMVC.ExportablePack<PureText>,
    public defensiveIdBlockPkg?: EasyMVC.ExportablePack<PureText>,
    public negativeIdBlockPkg?: EasyMVC.ExportablePack<PureText>,
    public dispelIdBlockPkg?: EasyMVC.ExportablePack<PureText>,
    public boostTablePkg?: EasyMVC.ExportablePack<PureText>,
    public healingTablePkg?: EasyMVC.ExportablePack<PureText>,
    public defensiveTablePkg?: EasyMVC.ExportablePack<PureText>,
    public negativeTablePkg?: EasyMVC.ExportablePack<PureText>,
    public ailmentTablePkg?: EasyMVC.ExportablePack<PureText>,
    public dispelTablePkg?: EasyMVC.ExportablePack<PureText>,
    public hybridTablePkg?: EasyMVC.ExportablePack<PureText>,
    public afflictionTablePkg?: EasyMVC.ExportablePack<PureText>,
    public specialSkillsPkg?: EasyMVC.ExportablePack<PureText>,
    public mahankaraBehaviorTablePkg?: EasyMVC.ExportablePack<PureText>,
    public mahankaraCategoriesPkg?: EasyMVC.ExportablePack<PureText>,
    public mahankaraGroupingsPkg?: EasyMVC.ExportablePack<PureText>,
    public mahankaraConcatenationsPkg?: EasyMVC.ExportablePack<PureText>,
    public categoriesXStressStatesPkg?: EasyMVC.ExportablePack<PureText>,
    public subContextPkg?: EasyMVC.ExportablePack<PureText>,
    public attributeCheckPkg?: EasyMVC.ExportablePack<PureText>,
    public attributediceFrustrationPkg?: EasyMVC.ExportablePack<PureText>,
    public uniqueCharactersByHCAndBLPkg?: EasyMVC.ExportablePack<PureText>,
    public knowledgePkg?: EasyMVC.ExportablePack<PureText>,
    public subContextKnowledgePkg?: EasyMVC.ExportablePack<PureText>,
    public situationalModifierPkg?: EasyMVC.ExportablePack<PureText>,
    public dCKnowledgeGuidePkg?: EasyMVC.ExportablePack<PureText>,
    public knowledgeDiceFrustrationPkg?: EasyMVC.ExportablePack<PureText>,
    public knowledgeCheckPkg?: EasyMVC.ExportablePack<PureText>,
    public archetypeListPkg?: EasyMVC.ExportablePack<PureText>,
    public totalArchetypesPkg?: EasyMVC.ExportablePack<PureText>,
    public castsGoldenPkg?: EasyMVC.ExportablePack<PureText>,
    public castsSoulsPkg?: EasyMVC.ExportablePack<PureText>,
    public castsSigilosPkg?: EasyMVC.ExportablePack<PureText>,
    public amplifiersPkg?: EasyMVC.ExportablePack<PureText>,
    public spokePlacePkg?: EasyMVC.ExportablePack<PureText>,
    public cTREVMAXPkg?: EasyMVC.ExportablePack<PureText>,
    public cTRCollectiblePkg?: EasyMVC.ExportablePack<PureText>,
    public lUKCollectiblePkg?: EasyMVC.ExportablePack<PureText>,
    public iNTCollectiblePkg?: EasyMVC.ExportablePack<PureText>,
    public sPDCollectiblePkg?: EasyMVC.ExportablePack<PureText>,
    public aiPromptPkg?: EasyMVC.ExportablePack<PureText>,
    public openAIEnvironmentPkg?: EasyMVC.ExportablePack<PureText>,
    public openAIKeyGeneraltPkg?: EasyMVC.ExportablePack<PureText>,
    public healingChangePerTurnPARTYPkg?: EasyMVC.ExportablePack<PureText>,
    public healingChangePerTurnBOOSpkg?: EasyMVC.ExportablePack<PureText>,
    public comboTablePkg?: EasyMVC.ExportablePack<PureText>,
    public missionNotesPkg?: EasyMVC.ExportablePack<PureText>,
    public chaosIdBlockPkg?: EasyMVC.ExportablePack<PureText>,
    public chaosTablePkg?: EasyMVC.ExportablePack<PureText>,
    public rpCTableTributeAndSubmissionPkg?: EasyMVC.ExportablePack<PureText>,
    public rpCEscapeTablePkg?: EasyMVC.ExportablePack<PureText>,
    public modMoonAttributesPkg?: EasyMVC.ExportablePack<PureText>,
    public modMoonRangesPkg?: EasyMVC.ExportablePack<PureText>,
    public parryPryPkg?: EasyMVC.ExportablePack<PureText>,

  ) {
    super({ exportedDateTime, appVersion });
  }

  static New(file: DSO9): DSO9 {
    const newDSO6 = Object.assign(new DSO9(), file);
    (newDSO6 as any).appVersion = file.appVersion;
    (newDSO6 as any).exportedDateTime = file.exportedDateTime;
    return newDSO6;
  }
  readonly fileFormat: Format = 'dso9';
}
