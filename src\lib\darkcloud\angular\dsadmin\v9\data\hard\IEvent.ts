import { EventType } from 'src/lib/darkcloud/dialogue-system';
import { IStoryProgress } from './IStoryProgress';

export interface IEvent extends IStoryProgress, EventParameters {
  type?: EventType;
}

export interface EventParameters {
  missionId?: string;
  objectiveId?: string;

  itemId?: string;
  amount?: number;

  videoId?: string;
  tutorialId?: string;

  characterId?: string;
  microloopId?: string;

  isPercentage?:boolean
}

export type Requirements<T> = { [key in keyof T]: any[] };
export type Index<TValue> = { [index: number]: TValue };

export const invalidRequirementsByEventTypes: Index<
  Requirements<EventParameters>
> = {
  [EventType.DIARY_EVENT]: {
    itemId: [undefined],
    amount: [undefined, null, NaN],
  },
  [EventType.RECEIVE_ITEM]: {
    itemId: [undefined],
    amount: [undefined, null, NaN],
  },
  [EventType.GIVE_ITEM]: {
    itemId: [undefined],
    amount: [undefined, null, NaN],
  },
  [EventType.TRADE_ITEM]: {
    itemId: [undefined],
    amount: [undefined, null, NaN],
  },
  [EventType.REMOVE_SPECIAL_ITEM]: {
    itemId: [undefined],
    amount: [undefined, null, NaN]
  },
  [EventType.ASSIGN_MISSION]: {
    missionId: [undefined],
  },
  [EventType.PLAY_VIDEO]: {
    videoId: [undefined],
  },
  [EventType.PLAY_TUTORIAL]: {
    tutorialId: [undefined],
  },
  [EventType.COMPLETE_OBJECTIVE]: {
    missionId: [undefined],
    objectiveId: [undefined],
  },
  [EventType.FAIL_OBJECTIVE]: {
    missionId: [undefined],
    objectiveId: [undefined],
  },
  [EventType.LINK_MICROLOOP]: {
    microloopId: [undefined]
  }
};

export function getRequirementsByType<
  T,
  TRequirements = Requirements<T>,
  TIndex extends Index<TRequirements> = Index<TRequirements>,
  TKey extends keyof TIndex = keyof TIndex
>(index: TIndex, t: TKey) {
  return index[t] === undefined ? [] :
  (Object.keys(index[t]) as (keyof TIndex[TKey])[]).map((key) => {
    return { key: key as keyof TIndex[TKey], invalidValues: index[t][key] };
  });
}
