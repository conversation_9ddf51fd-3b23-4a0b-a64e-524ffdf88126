import { MarkerType } from 'src/lib/darkcloud/dialogue-system';
import { IStoryProgress } from './IStoryProgress';

export interface IMarker extends IStoryProgress, MarkerParameters {
  type?: MarkerType;
  idMessageType?: string;
  contextType?:string;
  unlockCondition?:string;
  choosedCondition?:string;
  conditionOperator?:string;
  nameAtackAdvantage?:string;
  amountRequired?:number;
  knowledgePointValue?:number;
  knowledgeId?:string; //knowledge List no Others
}

export interface MarkerParameters {
  levelId?: string;
  pin?: boolean;
  characterId?: string;
  microloopId?: string;
  origin?: number;

  unlockCondition?:string;
  choosedCondition?:string;
  conditionOperator?:string;
  amountRequired?:number;
  nameAtackAdvantage?:string;
  knowledgePointValue?:number;
  knowledgeId?:string;
}
export type MarkerRequirements<T> = { [key in keyof T]: any[] };
export type IndexMarker<TValue> = { [index: number]: TValue };

//Validates the fields of the Marker component and adds it to the Review in Missiong
export const invalidRequirementsByMarkerTypes: IndexMarker<
  MarkerRequirements<MarkerParameters>
> = {
  [MarkerType.UNLOCK_LEVEL]: {
    levelId: [undefined, ""],
  },
  [MarkerType.RELEASE_DIALOGUE]: {
    levelId: [undefined, ""],
    pin: [],
  },
  [MarkerType.BOSS_TYPE]: {
    levelId: [undefined, ""],
  }, 
  [MarkerType.PIN]: {
    levelId: [undefined, ""],
  },
  [MarkerType.MARK_COLLECTIBLE]: {
    characterId: [undefined, ""],
  },
  [MarkerType.MICROLOOP]: {
    microloopId: [undefined, ""],
  },
  [MarkerType.RESTART_DIALOGUE]: {  
    unlockCondition: [undefined, ""],    
  },
  [MarkerType.ATTACK_ADVANTAGE]: {
    nameAtackAdvantage: [undefined, ""],
  },
  [MarkerType.KNOWLEDGE_POINT]: {
    knowledgePointValue: [undefined, null, NaN, ""],
    knowledgeId: [undefined, null, NaN, ""]
  },
  
};

export function getMarkerRequirementsByType<T,TRequirements = MarkerRequirements<T>, TIndex extends IndexMarker<TRequirements> = IndexMarker<TRequirements>,
  TKey extends keyof TIndex = keyof TIndex>(index: TIndex, t: TKey) {
  return index[t] === undefined ? [] :
  (Object.keys(index[t]) as (keyof TIndex[TKey])[]).map((key) => {
    return { key: key as keyof TIndex[TKey], invalidValues: index[t][key] };
  });
}
