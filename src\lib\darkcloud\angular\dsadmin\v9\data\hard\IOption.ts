import { IBase } from './IBase';

export interface IOption extends IBase {
  answerBoxId?: string;
  answerBoxNegativeId?: string;
  isOmitted?: boolean;
  isModMoon?: boolean;
  message?: string;
  weight?: number;
  label?: string;
  nameKnowledge?: string;
  fatorSituationModifier?: string;
  valueSituationModifier?: number;
  choiceSpeech?: string;
  choiceAtributte?: string;
  choiceNegative?: string;
  labelAnswerNegative?: string;
  choicePositive?: string;
  investigationPositive?: string;
  investigationNegative?: string;
  labelAnswerPositive?: string;
  choiceDifficulty?: string;
  investigaDifficulty?: string;
  classeNameOpponet?: string;
  difficultClassValue?: string;
  classModifierValue?: number;
  descriptionDCGuide?: string;
  descriptionInvestigation?: string;
  descriptionSituationalModifier?: string;
  resultDC?: number;
  valueModifierITD?: number;
  bl?: number;
  subcontext?: string;
  subContextDescription?: string;
  type?: number; //get value  CHOICE = 0, INVESTIGATION = 1
  //If it is AND all the roadblocks must be unlocked for the SB be enabled. If OR any roadblock is enough. OR is the default.
  AndOrCondition?: string;
}
