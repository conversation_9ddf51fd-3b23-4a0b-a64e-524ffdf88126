import { IBase } from './IBase';
export interface IStoryBox extends IBase {
  storyProgressIds: string[];
  choiceNegative?: string;
  choicePositive?: string;
  investigationPositive?: string;
  investigationNegative?: string;
  label?: string;
  labelOption?: string;
  resultDCOption?: number;
  messageOption?: string;
  descriptionDCGuideOption?: string;
  descriptionKnowledge?: string;
  subContextDescription?: string;
  subcontext?: string;
  nameKnowledge?: string;
  type?: number; //get value   CHOICE = 0, INVESTIGATION = 1
  //If it is AND all the roadblocks must be unlocked for the SB be enabled. If OR any roadblock is enough. OR is the default.
  AndOrCondition?: string;
  fatorSituationModifier?: string;
  valueSituationModifier?: number;
  descriptionSituationalModifier?: string;
}
