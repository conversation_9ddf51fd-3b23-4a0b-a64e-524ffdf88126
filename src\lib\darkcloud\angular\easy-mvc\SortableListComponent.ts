import { ListComponent } from './ListComponent';
import { comparable, comparableNumber, extractInt } from 'src/lib/others';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Sorting } from './Sorting';
import { GameTypes } from 'src/lib/darkcloud/dialogue-system';
import { IModel } from './IModel';
import { ModelService } from './ModelService';
import { ActivatedRoute } from '@angular/router';


export abstract class SortableListComponent<Model extends IModel> extends ListComponent<Model> 
{
  constructor(
    protected override _modelService: ModelService<Model>,
    protected override _activatedRoute: ActivatedRoute,
    protected override _userSettingsService: UserSettingsService,
    protected srtLstParameter: Sorting.Parameter
  ) 
  {
    super(_modelService, _activatedRoute, _userSettingsService);
  }

  protected srtLstOrder: Sorting.Order = 'descending';

  public sortListByParameter(parameter: Sorting.Parameter, index: number = undefined) {
    this.srtLstOrder = Sorting.nextOrder(
      this.srtLstOrder,
      this.srtLstParameter,
      parameter
    );

    this.sort(parameter, index);
    this._modelService.toSave();
    this.lstIds = this.getDataIds(this._modelService.svcFilterByIds(this.lstIds));
    this.srtLstParameter = parameter;
  }

  /*
  This method is new and used to substitute the old one, the above one with the name sortListByParameter.
  This method sometimes do not work with the lstIds list.
  This one below receives the list and the parameter which the list will the sorted. By the end the method return the list 
  sorted.
  */ 
  sortAscendingChar = true;
  areaSortAscending = false; // Começa como false para que o primeiro clique seja ascendente

  sortListByIdCharacter() {

    this._modelService.models.sort((a, b) => {
      const idA = parseInt(a.id.replace("C", ""), 10);
      const idB = parseInt(b.id.replace("C", ""), 10);

      if (this.sortAscendingChar) {
        return idA - idB;
      } else {
        return idB - idA;
      }
    });
    this.lstIds = this.getDataIds(this._modelService.svcFilterByIds(this.lstIds));
    this.sortAscendingChar = !this.sortAscendingChar;
  }

  sortListClass() {
    // Alternar o estado da ordenação ANTES de aplicar a ordenação
    this.sortAscendingChar = !this.sortAscendingChar;

    // Separar itens com classId undefined dos demais
    const itemsWithClass = this._modelService.models.filter(item => item['classId'] !== undefined);
    const itemsWithoutClass = this._modelService.models.filter(item => item['classId'] === undefined);

    // Ordenar apenas os itens que têm classe definida
    itemsWithClass.sort((a, b) => {
      // Obter os nomes completos das classes
      const classNameA = this.getClassName(a['classId']);
      const classNameB = this.getClassName(b['classId']);
     
      // Ordenação alfabética simples pelos nomes das classes
      const comparison = classNameA.localeCompare(classNameB, 'pt-BR', {
        numeric: true,
        sensitivity: 'base'
      });

      const result = this.sortAscendingChar ? comparison : -comparison; 
     return result;
    });

    // Reconstruir a lista: itens com classe + itens sem classe no final
    this._modelService.models = [...itemsWithClass, ...itemsWithoutClass];

    // Log dos primeiros nomes após ordenação
    const firstClassNames = itemsWithClass.slice(0, 10).map(item => {
      return this.getClassName(item['classId']);
    });
   
    // Atualizar a lista de IDs respeitando os filtros aplicados
    this.lstFetchLists(true); // Força refresh dos filtros 
  }
  
    sortListMissionByArea() {

    this._modelService.models.sort((a, b) => {
      // Verificar se algum dos areaId é undefined
      const areaIdA = a['areaId'];
      const areaIdB = b['areaId'];

      // Se ambos são undefined, manter ordem original
      if (areaIdA === undefined && areaIdB === undefined) {
        return 0;
      }

      // Se apenas A é undefined, colocar A na posição 'U' (após as outras áreas)
      if (areaIdA === undefined && areaIdB !== undefined) {
        // Comparar 'U' com o nome da área B
        const comparison = 'U'.localeCompare(this.getAreaName(areaIdB), 'pt-BR', {
          numeric: true,
          sensitivity: 'base'
        });
        return this.areaSortAscending ? comparison : -comparison;
      }

      // Se apenas B é undefined, colocar B na posição 'U' (após as outras áreas)
      if (areaIdA !== undefined && areaIdB === undefined) {
        // Comparar nome da área A com 'U'
        const comparison = this.getAreaName(areaIdA).localeCompare('U', 'pt-BR', {
          numeric: true,
          sensitivity: 'base'
        });
        return this.areaSortAscending ? comparison : -comparison;
      }

      // Ambos têm areaId definido, fazer comparação alfabética normal
      const areaNameA = this.getAreaName(areaIdA);
      const areaNameB = this.getAreaName(areaIdB);

      // Comparação alfabética case-insensitive
      const comparison = areaNameA.toLowerCase().localeCompare(areaNameB.toLowerCase(), 'pt-BR', {
        numeric: true,
        sensitivity: 'base'
      });

      if (this.areaSortAscending) {
        return comparison; // Ordena de forma crescente (A-Z)
      } else {
        return -comparison; // Ordena de forma decrescente (Z-A)
      }
    });

    // Atualizar a lista de IDs
    this.lstIds = this.getDataIds(this._modelService.svcFilterByIds(this.lstIds));
 
    // Alternar o estado da ordenação para o próximo clique
    this.areaSortAscending = !this.areaSortAscending;
  }

  sortListCharacterByArea() {

    // Alternar o estado da ordenação ANTES de aplicar a ordenação
    this.areaSortAscending = !this.areaSortAscending;

    // Separar itens com areaId undefined dos demais
    const itemsWithArea = this._modelService.models.filter(item => item['areaId'] !== undefined);
    const itemsWithoutArea = this._modelService.models.filter(item => item['areaId'] === undefined);

    // Ordenar apenas os itens que têm área definida
    itemsWithArea.sort((a, b) => {
      // Obter os nomes completos das áreas
      const areaNameA = this.getAreaName(a['areaId']);
      const areaNameB = this.getAreaName(b['areaId']);

      // Ordenação alfabética simples pelos nomes das áreas
      const comparison = areaNameA.localeCompare(areaNameB, 'pt-BR', {
        numeric: true,
        sensitivity: 'base'
      });

      const result = this.areaSortAscending ? comparison : -comparison;
      return result;
    });

    // Reconstruir a lista: itens com área + itens sem área no final
    this._modelService.models = [...itemsWithArea, ...itemsWithoutArea];

    // Log dos primeiros códigos após ordenação
    const firstCodes = itemsWithArea.slice(0, 10).map(item => {
      const areaName = this.getAreaName(item['areaId']);
      const colonIndex = areaName.indexOf(':');
      return colonIndex === -1 ? areaName.trim().charAt(0).toUpperCase() : areaName.substring(0, colonIndex).trim();
    });

    // Atualizar a lista de IDs respeitando os filtros aplicados
    this.lstFetchLists(true); // Força refresh dos filtros
  }

  // Método auxiliar para obter o nome da área (deve ser sobrescrito nos componentes filhos)
  protected getAreaName(areaId: string): string {
    // Implementação padrão - retorna o próprio areaId
    // Este método deve ser sobrescrito nos componentes que usam áreas
    return areaId || '';
  }

  // Método auxiliar para obter o nome da classe (deve ser sobrescrito nos componentes filhos)
  protected getClassName(classId: string): string {
    // Implementação padrão - retorna o próprio classId
    // Este método deve ser sobrescrito nos componentes que usam classes
    return classId || '';
  }

  sortParameterOrder = -1;
  public sortListByName(list, parameter)
  {
    this.sortParameterOrder *= -1;
    list.sort((a, b) => 
    {
      a = this.simplifyString(a[parameter])
      b = this.simplifyString(b[parameter])
      return this.sortParameterOrder * a?.localeCompare(b);
    });
  
    return list
  }

  public sortListByNumber(list, parameter)
  {
    this.sortParameterOrder *= -1;  
    list.sort((a,b)=>
    {
      if(!a[parameter] && b[parameter]) return 1
      if(a[parameter] && !b[parameter]) return -1
      if(!a[parameter] && !b[parameter]) return 0 
      return this.sortParameterOrder*a[parameter]-b[parameter]
    });
 
    return list
  }
/*This is a kind of list that is inside another list. This is a property inside an interface. For example, status is a 
field inside the ISilicatos. Status is a sublist!
*/
    public sortSublistByNumber(list, parameter, index)
    {
      this.sortParameterOrder *= -1;  
      list.sort((a,b)=>
      {
        if(!a[parameter][index] && b[parameter][index]) return 1
        if(a[parameter][index] && !b[parameter][index]) return -1
        if(!a[parameter][index] && !b[parameter][index]) return 0 
        return this.sortParameterOrder*a[parameter][index]-b[parameter][index]
      });
   
        return list
    }

  simplifyString(str: string): string 
  {
    return (str?.normalize('NFD')?.replace(/[\u0300-\u036f]/g, '')?.toLocaleUpperCase())?.trim();
  }

  /**
   * Generic method to sort models by their relationship with reference models
   * This method categorizes models into groups based on their reference relationships:
   * - No references: Models that have no reference IDs
   * - Not assigned: Models with reference IDs that don't exist in the reference service
   * - Single assigned: Models with reference IDs that appear once in the reference service
   * - Multiple assigned: Models with reference IDs that appear multiple times in the reference service
   *
   * @param referenceService The service containing the reference models to check against
   * @param referenceIdField The field name in the main models that contains the reference IDs array
   * @param referenceField The field name in the reference models to match against
   * @param resultArrays Object containing the arrays to populate with categorized results
   * @param invertCounter Counter for cycling through different sort orders
   * @returns The sorted and concatenated result array
   */
  protected sortModelsByReferences<RefModel extends IModel>(
    referenceService: ModelService<RefModel>,
    referenceIdField: string,
    referenceField: string,
    resultArrays: {
      noReferences: Model[],
      noAssigned: Model[],
      singleAssigned: Model[],
      multipleAssigned: Model[],
      result: Model[]
    },
    invertCounter: { value: number }
  ): Model[] {
    // Initialize result arrays
    resultArrays.noAssigned = [];
    resultArrays.noReferences = [];
    resultArrays.singleAssigned = [];
    resultArrays.multipleAssigned = [];

    let canBreak: boolean = false;
    let goToSingleMultiple: boolean = false;

    ////////////////////////// No References ///////////////////////////////
    for (let i = 0; i < this._modelService.models.length; i++) {
      goToSingleMultiple = false;
      const currentModel = this._modelService.models[i];
      const referenceIds = currentModel[referenceIdField];

      if (!referenceIds || referenceIds.length === 0) {
        resultArrays.noReferences.push(currentModel);
      }
      else {
        ///////////////////////////// Not Assigned //////////////////////////////
        let canContinue: boolean = false;
        let counter: number = 0;

        // Verify if at least one reference ID does not have a match in the reference service
        for (let k = 0; k < referenceIds.length; k++) {
          for (let j = 0; j < referenceService.models.length; j++) {
            if (referenceIds[k] === referenceService.models[j][referenceField]) {
              canContinue = true;
              counter = counter + 1;
              break;
            }
            // If at least one reference ID does not have a match, mark as not assigned
            else if (j === referenceService.models.length - 1) {
              resultArrays.noAssigned.push(currentModel);
              canBreak = true;
            }
          }
          if (canContinue) {
            canContinue = false;
            continue;
          }
          if (canBreak) {
            canBreak = false;
            break;
          }
        }

        if (counter === referenceIds.length) goToSingleMultiple = true;

        ////////////////////////// Single and Multiple ///////////////////////////////////
        if (goToSingleMultiple) {
          let isRepeated: boolean = false;
          let locations = [];

          for (let k = 0; k < referenceIds.length; k++) {
            for (let j = 0; j < referenceService.models.length; j++) {
              if (referenceIds[k] === referenceService.models[j][referenceField]) {
                if (!locations.some(obj => obj[referenceField] === referenceIds[k])) {
                  locations.push(referenceService.models[j]);
                }
                else {
                  isRepeated = true;
                }
              }
            }
          }

          if (isRepeated) {
            resultArrays.multipleAssigned.push(currentModel);
          } else {
            resultArrays.singleAssigned.push(currentModel);
          }
        }
      }
    }

    ////////////////////////////// Result Concatenation /////////////////////////////////////
    resultArrays.result = [];
    invertCounter.value = invertCounter.value + 1;

    if (invertCounter.value % 4 === 1) {
      resultArrays.result = resultArrays.noAssigned
        .concat(resultArrays.noReferences)
        .concat(resultArrays.singleAssigned)
        .concat(resultArrays.multipleAssigned);
    }
    else if (invertCounter.value % 4 === 2) {
      resultArrays.result = resultArrays.noReferences
        .concat(resultArrays.singleAssigned)
        .concat(resultArrays.multipleAssigned)
        .concat(resultArrays.noAssigned);
    }
    else if (invertCounter.value % 4 === 3) {
      resultArrays.result = resultArrays.singleAssigned
        .concat(resultArrays.multipleAssigned)
        .concat(resultArrays.noAssigned)
        .concat(resultArrays.noReferences);
    }
    else if (invertCounter.value % 4 === 0) {
      resultArrays.result = resultArrays.multipleAssigned
        .concat(resultArrays.noAssigned)
        .concat(resultArrays.noReferences)
        .concat(resultArrays.singleAssigned);
    }

    // Update the main model service with the sorted results
    this._modelService.models = [...resultArrays.result];

    // Update lstIds to reflect the new order
    this.lstIds = this.getDataIds(this._modelService.svcFilterByIds(this.lstIds));

    return resultArrays.result;
  }

  /**
   * Method for mission-objective sorting that can be called directly from HTML templates
   * This method uses the generic sortModelsByReferences but requires child components to provide
   * the necessary services and properties through abstract methods
   */
  public sortMissionObjectives(): void {
    // Check if the component has the required methods and properties
    if (this.hasObjectiveSortingCapability()) {
      const referenceService = this.getReferenceService();
      const resultArrays = this.getObjectiveSortingArrays();
      const invertCounter = this.getInvertCounter();

      const sortedResults = this.sortModelsByReferences(
        referenceService,
        'objectiveIds',
        'objectiveId',
        resultArrays,
        invertCounter
      );

      // Update the result array in the component
      this.updateSortedResults(sortedResults);
    } else {
      console.warn('sortMissionObjectives called but component does not implement objective sorting capability');
    }
  }

  /**
   * Method for sorting missions by ID that can be called directly from HTML templates
   * This method sorts missions by extracting the numeric part of their IDs
   */
  public sortMissionListById(): void {
    // Check if the component has the required methods and properties
    if (this.hasMissionIdSortingCapability()) {
      const sortAscending = this.getMissionIdSortAscending();
      const missions = this.getMissionArray();

      this.sortMissionsById(missions, sortAscending);

      // Update the component with sorted results
      this.updateMissionIdSortedResults(missions);
    } else {
      console.warn('sortMissionListById called but component does not implement mission ID sorting capability');
    }
  }

  /**
   * Generic method to sort missions by their ID (extracting numeric part)
   */
  protected sortMissionsById(missions: Model[], sortAscending: { value: boolean }): void {
    missions.sort((a, b) => {
      // Extrai o número do id, removendo a letra "M" e convertendo para número
      const idA = parseInt(a.id.replace("M", ""), 10);
      const idB = parseInt(b.id.replace("M", ""), 10);

      if (sortAscending.value) {
        return idA - idB; // Ordena de forma crescente
      } else {
        return idB - idA; // Ordena de forma decrescente
      }
    });

    // Toggle the sort order for next click
    sortAscending.value = !sortAscending.value;

    // Update the main model service with the sorted results
    this._modelService.models = [...missions];

    // Update lstIds to reflect the new order
    this.lstIds = this.getDataIds(this._modelService.svcFilterByIds(this.lstIds));
  }

  
  /**
   * Abstract methods that must be implemented by child components that want to use sortMissionObjectives
   * These methods provide the necessary services and properties for the sorting operation
   */

  // Check if the component has objective sorting capability
  protected hasObjectiveSortingCapability(): boolean {
    return false; // Default implementation - override in child components
  }

  // Get the reference service (e.g., EventService)
  protected getReferenceService(): ModelService<any> {
    throw new Error('getReferenceService must be implemented by child component');
  }

  // Get the arrays for storing sorted results
  protected getObjectiveSortingArrays(): {
    noReferences: Model[],
    noAssigned: Model[],
    singleAssigned: Model[],
    multipleAssigned: Model[],
    result: Model[]
  } {
    throw new Error('getObjectiveSortingArrays must be implemented by child component');
  }

  // Get the invert counter object
  protected getInvertCounter(): { value: number } {
    throw new Error('getInvertCounter must be implemented by child component');
  }

  // Update the component with sorted results
  protected updateSortedResults(sortedResults: Model[]): void {
    // Default implementation - can be overridden in child components
  }

  /**
   * Abstract methods for mission sorting capability
   */

  // Check if the component has mission sorting capability
  protected hasMissionSortingCapability(): boolean {
    return false; // Default implementation - override in child components
  }

  // Get the arrays for storing mission sorted results
  protected getMissionSortingArrays(): {
    noAssigned: Model[],
    singleAssigned: Model[],
    multipleAssigned: Model[],
    result: Model[]
  } {
    throw new Error('getMissionSortingArrays must be implemented by child component');
  }

  // Get the mission invert counter object
  protected getMissionInvertCounter(): { value: number } {
    throw new Error('getMissionInvertCounter must be implemented by child component');
  }

  // Update the component with mission sorted results
  protected updateMissionSortedResults(sortedResults: Model[]): void {
    // Default implementation - can be overridden in child components
  }

  /**
   * Abstract methods for mission ID sorting capability
   */

  // Check if the component has mission ID sorting capability
  protected hasMissionIdSortingCapability(): boolean {
    return false; // Default implementation - override in child components
  }

  // Get the sort ascending flag (by reference)
  protected getMissionIdSortAscending(): { value: boolean } {
    throw new Error('getMissionIdSortAscending must be implemented by child component');
  }

  // Get the mission array to sort
  protected getMissionArray(): Model[] {
    throw new Error('getMissionArray must be implemented by child component');
  }

  // Update the component with mission ID sorted results
  protected updateMissionIdSortedResults(sortedResults: Model[]): void {
    // Default implementation - can be overridden in child components
  }

  /**
   * Method for mission-mission sorting that can be called directly from HTML templates
   * This method handles the specific logic for sorting missions by their assignment status
   */
  public sortMissionMissions(): void {
    // Check if the component has the required methods and properties
    if (this.hasMissionSortingCapability()) {
      const referenceService = this.getReferenceService();
      const resultArrays = this.getMissionSortingArrays();
      const invertCounter = this.getMissionInvertCounter();

      this.sortMissionsByAssignment(referenceService, resultArrays, invertCounter);

      // Update the result array in the component
      this.updateMissionSortedResults(resultArrays.result);
    } else {
      console.warn('sortMissionMissions called but component does not implement mission sorting capability');
    }
  }

  /**
   * Generic method to sort missions by their assignment status in events
   */
  protected sortMissionsByAssignment<RefModel extends IModel>(
    referenceService: ModelService<RefModel>,
    resultArrays: {
      noAssigned: Model[],
      singleAssigned: Model[],
      multipleAssigned: Model[],
      result: Model[]
    },
    invertCounter: { value: number }
  ): void {
    // Initialize result arrays
    resultArrays.noAssigned = [];
    resultArrays.singleAssigned = [];
    resultArrays.multipleAssigned = [];

    let goToSingleMultiple: boolean = false;

    for (let j = 0; j < this._modelService.models.length; j++) {
      goToSingleMultiple = false;
      const currentModel = this._modelService.models[j];

      /////////////////////////////// NoAssigned /////////////////////////////////
      for (let i = 0; i < referenceService.models.length; i++) {
        const referenceModel = referenceService.models[i];

        // Just the assign mission is counted as mission, the others are objectives and fail
        if (referenceModel['missionId'] === currentModel.id && referenceModel['type'] === 3) {
          goToSingleMultiple = true;
          break;
        }
        else if (i === referenceService.models.length - 1) {
          resultArrays.noAssigned.push(currentModel);
        }
      }

      ////////////////////////// Single and Multiple ///////////////////////////////////
      if (goToSingleMultiple) {
        let aux = [];
        for (let i = 0; i < referenceService.models.length; i++) {
          const referenceModel = referenceService.models[i];

          if (referenceModel['missionId'] === currentModel.id &&
            !aux.some(obj => obj === referenceModel.id) &&
            // Just the assign mission is counted as mission, the others are objectives and fail
            referenceModel['type'] === 3) {
            aux.push(referenceModel.id);
          }
        }

        if (aux.length === 1) {
          resultArrays.singleAssigned.push(currentModel);
        } else {
          resultArrays.multipleAssigned.push(currentModel);
        }
      }
    }

    ////////////////////////////// Result Concatenation /////////////////////////////////////
    resultArrays.result = [];
    invertCounter.value = invertCounter.value + 1;

    if (invertCounter.value % 3 === 1) {
      resultArrays.result = resultArrays.noAssigned
        .concat(resultArrays.singleAssigned)
        .concat(resultArrays.multipleAssigned);
    }
    else if (invertCounter.value % 3 === 2) {
      resultArrays.result = resultArrays.singleAssigned
        .concat(resultArrays.multipleAssigned)
        .concat(resultArrays.noAssigned);
    }
    else if (invertCounter.value % 3 === 0) {
      resultArrays.result = resultArrays.multipleAssigned
        .concat(resultArrays.noAssigned)
        .concat(resultArrays.singleAssigned);
    }

    // Update the main model service with the sorted results
    this._modelService.models = [...resultArrays.result];

    // Update lstIds to reflect the new order
    this.lstIds = this.getDataIds(this._modelService.svcFilterByIds(this.lstIds));
  }

  /**
   * Sorts list by parameter depending on which parameter it is
   * @param p Parameter to sort the list by
   */
  private sort(p: Sorting.Parameter, index: number = undefined) {

    switch (p) {
      case 'birthyear':
      case 'silicatoName':        
      case 'soulsCost':        
      case 'height':
      case 'playerLevel':
      case 'astralPlan':
      case 'totalXP':
      case 'timeToUpgrade':
      case 'rubies':
      case 'order':
      case 'bonusValue':
      case 'conditionValue':
      case 'conditionDuration':
      case 'labLevel':
      case 'improveTitanium':
      case 'improveTime':
      case 'improveRubies':
      case 'researchSouls':
      case 'researchTime':
      case 'researchRubies':
      case 'titaniumLevel':
      case 'souls':
      case 'time':
      case 'storage':
      case 'production':
      case 'commonCodeBlocksAmount':
      case 'rareCodeBlocksAmount':
      case 'epicCodeBlocksAmount':
      case 'legendaryCodeBlocksAmount':
      case 'elementarCodeBlocksAmount':
      case 'skill':
      case 'acronym':
      case 'statusLevel':

      case 'elementarGoldAmount':
      case 'commonGoldAmount':
      case 'rareGoldAmount':
      case 'epicGoldAmount':
      case 'legendaryGoldAmount':
      case 'baseLevelUnlock':
       
        this._modelService.models.sort((a, b) => 
        {
  
          if (this.srtLstOrder === 'undefinedOnTop') 
          {
            return comparableNumber(a[p]) === undefined && comparableNumber(b[p]) !== undefined ? -1 : 1;
          } 
          else 
          {
            const compA = this.srtLstOrder === 'ascending' ? comparableNumber(a[p]) : comparableNumber(b[p]);
            const compB = this.srtLstOrder === 'ascending' ? comparableNumber(b[p]) : comparableNumber(a[p]);
            return compB !== undefined && compA !== undefined ? compA > compB ? -1 : 1
              : comparableNumber(a[p]) === undefined ? undefined : -1;
          }
        });
        break;
      case 'id':
      case 'areaId':
        this._modelService.models.sort((a, b) => 
        {
          return this.srtLstOrder === 'ascending' ? extractInt(a[p]) == extractInt(b[p]) ? 1 : -1
            : extractInt(b[p]) || extractInt(a[p]) === undefined ? -1 : 1;
        });
        break;
        
      case 'isCollectible':
        this._modelService.models.sort((a, b) => this.srtLstOrder === 'ascending' ? a[p] && !b[p]
              ? -1 : 1 : !a[p] && b[p] ? -1 : 1);
        break;
      case 'gender':
        this._modelService.models.sort((a, b) => 
        {
          if (this.srtLstOrder === 'undefinedOnTop') 
          {
            return a[p] === undefined && b[p] !== undefined ? -1 : 1;
          } 
          else 
          {
            const compA = this.srtLstOrder === 'ascending' ? GameTypes.genderName[a[p]] : GameTypes.genderName[b[p]];
            const compB = this.srtLstOrder === 'ascending' ? GameTypes.genderName[b[p]] : GameTypes.genderName[a[p]];
            return compB !== undefined && compA !== undefined ? compA > compB ? -1 : 1
             : GameTypes.genderName[a[p]] === undefined ? 1 : -1;
          }
        });
        break;
      case 'rarity':
        this._modelService.models.sort((a, b) =>
        {
          if(!a[p] && !b[p]) return 0;          
          if(!a[p] && !!b[p]) return 1;          
          if(!!a[p] && !b[p]) return -1;
          
          return this.srtLstOrder === 'ascending' ? comparable(a[p]) < comparable(b[p]) ? 1 : -1
            : comparable(a[p]) > comparable(b[p]) ? 1 : -1});
        break;
      case 'amount':
        this._modelService.models.sort((a, b) => 
        {
          if (this.srtLstOrder === 'undefinedOnTop') 
          {
            return comparableNumber(a[p][index]) === undefined &&
              comparableNumber(b[p][index]) !== undefined ? -1 : 1;
          } 
          else 
          {
            const compA = this.srtLstOrder === 'ascending' ? comparableNumber(a[p][index]) : comparableNumber(b[p][index]);
            const compB = this.srtLstOrder === 'ascending' ? comparableNumber(b[p][index]) : comparableNumber(a[p][index]);
            return compB !== undefined && compA !== undefined ? compA > compB ? -1 : 1
              : comparableNumber(a[p][index]) === undefined ? 1 : -1;
          }
        });
        break;
      default:
        // alphabetical order
        this.specialSort(p);
        break;
    }
  }

  /**
   * Custom method that sorts the list and can be implemented on the component
   * @param p Parameter to sort the list by
   */
  protected specialSort(p: Sorting.Parameter) 
  {
    this.defaultSort(p);
  }

  /**
   * The default sorting of the list for any parameter
   * @param p Parameter to sort the list by
   */
  protected defaultSort(p: Sorting.Parameter) 
  {
    this._modelService.models.sort((a, b) =>
      this.srtLstOrder === 'ascending' ? comparable(a[p]) < comparable(b[p]) ? 
      1 : -1 : comparable(a[p]) > comparable(b[p]) ? 1 : -1 );
  }
}
