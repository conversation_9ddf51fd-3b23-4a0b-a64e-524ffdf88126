export enum EventType {
  RECEIVE_ITEM = 0,
  GIVE_ITEM = 1,
  T<PERSON><PERSON>_ITEM = 2,
  ASSIGN_MISSION = 3,
  PLAY_VIDEO = 4,
  PLAY_TUTORIAL = 5,
  COMPLETE_OBJECTIVE = 6,
  FAIL_OBJECTIVE = 7,
  REMOVE_SPECIAL_ITEM = 8,
  REMOVE_SPECIAL_ITEM_ON_GRIND = 12,
  REFUSE_PAYMENT = 9,
  EVADE = 10,
  PAY_BRIBE = 13,
  LINK_MICROLOOP = 11,
  DIARY_EVENT = 14
}

export enum EventCategory {
  MISSION = 0,
  ITEM = 1,
  CINEMATIC = 2,
  LOOP = 3
}

export const EventTypeToCategory = {
  [+EventType.DIARY_EVENT]: EventCategory.ITEM,
  [+EventType.RECEIVE_ITEM]: EventCategory.ITEM,
  [+EventType.GIVE_ITEM]: EventCategory.ITEM,
  [+EventType.TRADE_ITEM]: EventCategory.ITEM,
  [+EventType.REMOVE_SPECIAL_ITEM]: EventCategory.ITEM,
  [+EventType.REMOVE_SPECIAL_ITEM_ON_GRIND]: EventCategory.ITEM,
  [+EventType.ASSIGN_MISSION]: EventCategory.MISSION,
  [+EventType.COMPLETE_OBJECTIVE]: EventCategory.MISSION,
  [+EventType.FAIL_OBJECTIVE]: EventCategory.MISSION,
  [+EventType.PLAY_VIDEO]: EventCategory.CINEMATIC,
  [+EventType.PLAY_TUTORIAL]: EventCategory.CINEMATIC,
  [+EventType.REFUSE_PAYMENT]: EventCategory.LOOP,
  [+EventType.PAY_BRIBE]: EventCategory.LOOP,
  [+EventType.EVADE]: EventCategory.LOOP,
  [+EventType.LINK_MICROLOOP]: EventCategory.LOOP  
}

export const EventCategoryToType = {
  [+EventCategory.ITEM]: [
    EventType.DIARY_EVENT,
    EventType.GIVE_ITEM,
    EventType.RECEIVE_ITEM,
    EventType.TRADE_ITEM,
    EventType.REMOVE_SPECIAL_ITEM,
    EventType.REMOVE_SPECIAL_ITEM_ON_GRIND   
  ],
  [+EventCategory.MISSION]: [
    EventType.ASSIGN_MISSION,
    EventType.COMPLETE_OBJECTIVE,
    EventType.FAIL_OBJECTIVE
  ],
  [+EventCategory.CINEMATIC]: [
    EventType.PLAY_TUTORIAL,
    EventType.PLAY_VIDEO
  ],
  [+EventCategory.LOOP]: [
    EventType.EVADE,
    EventType.REFUSE_PAYMENT,
    EventType.LINK_MICROLOOP,
    EventType.PAY_BRIBE
  ]
}