export const enum IdPrefixes {
  SETTINGS = 'SETTINGS',
  AILMENT = 'AIL',
  AILMENTDEFENSES = 'AID',
  AMPLIFIERS = 'AMP',
  HEALINGCHANGEPERTURNPARTY = 'HCHParty',
  HEALINGCHANGEPERTURNBOSS = 'HCHBoss',
  RPCTA<PERSON>ETRIBUTEANDSUBMISSION = 'RPCSUB',
  RPCTABLEESCAPE = 'RPCESC',
  BONUS = 'BON',
  CONDITIONTIGGER = 'COND',
  DURATION = 'DUR',
  DCGUIDE = 'DCG',
  OPENAIENVIRONMENT = 'OPENAIENV',
  OPENAIKEYGENERAL = 'OPENAIKEYGEN',
  DCKNOWLEDGEGUIDE = 'DCKNG',
  CONFITHRESHOLD = 'CFT',
  CTREVMAX = 'CTRMAX',
  CATEGORY = 'CAT',
  REPETITION = 'REP',
  MAHANKARABEHAVIOR = 'MAHAB',
  MAHANKARACATEGORIES = 'MAHAC',
  CATEGORIESXSTRESSSTATES = 'CXS',
  CASTSGOLDEN = 'CASTGOLDEN',
  CASTSSOULS = 'CASTSOUL',
  CASTSSIGILOS = 'CASTSIGI', 
  MAHANKARAGROUPINGS = 'MAHAG',
  MAHANKARACONCATENATIONS = 'MAHACONCAT',
  BOOSTIDBLOCKS = 'BID',
  CHAOSIDBLOCKS = 'CHID',
  CHAOSTABLE = 'CHT',
  DEFENSIVEIDBLOCKS = 'DEF',
  AILMENTIDBLOCKS = 'AILID',
  DISPEL = 'DIS',
  NEGATIVE = 'NEG',
  HEALINGIDBLOCKS = 'HID',
  BOOSTTABLE = 'BOOSTT',
  HEALINGTABLE = 'HEALI',
  DEFENSIVETABLE = 'DEFT',
  AFFLICTIONTABLE = 'AFFT',
  NEGATIVETABLE = 'NEGT',
  HYBRIDTABLE = 'HYBT',
  COMBOTABLE = 'COMBO',
  AILMENTTABLE = 'AILT',
  DISPELTABLE = 'DIST',
  PASSIVEALLOWED = 'PASS',
  SPECIALSKILLS = 'SPSKILL',
  CTRCOLLECTIBLE = 'CTRCOLLECTIBLE',
  LUKCOLLECTIBLE = 'LUKCOLLECTIBLE',
  INTCOLLECTIBLE = 'INTCOLLECTIBLE',
  SPDCOLLECTIBLE = 'SPDCOLLECTIBLE',
  SUBCONTEXT = 'SUB',
  SUBCONTEXTKNOWLEDGE = 'SUBKNOW',
  SITUATIONALMODIFIER = 'SM',
  MODMOONRANGES = 'MMRANG',
  PARRYPRY = 'PPRY',
  ATTRIBUTECHECK = 'ATTC',
  KNOWLEDGECHECK =  'KNOC',
  ATTRIBUTEDICEFRUSTRATION = 'DFL',
  KNOWLEDGEDICEFRUSTRATION = 'KNOD',
  EMOTION = 'E',
  ITEM = 'IT',
  RELICUSES = 'RELIC',
  CHARACTER = 'C',
  MINION_STATS = 'MS',
  TUTORIAL = 'TU',
  VIDEO = 'V',
  ANIMATIC_THEME = 'TH',
  MISSION = 'M',
  OBJECTIVE = 'O',
  CLASS = 'K',
  AREA = 'A',
  LEVEL = 'L',
  LEVEL_UPGRADE = 'LU',
  SCENERY = 'S',
  DIALOGUE = 'D',
  OPTIONBOX = 'OB',
  DILEMMABOX = 'DIL',
  STORYBOX = 'SB',
  ANSWERDILEMMABOX = 'ADB',
  OPTION = 'opt',
  DILEMMA = 'dlm',
  SPEECH = 'spc',
  EVENT = 'evt',
  MARKER = 'mrk',
  KEYWORD = 'cw',
  SOUND = 'af',
  CONDITION = 'cnd',
  QNA = 'qna',
  ROADBLOCK = 'rb',
  MINIGAME = 'mg',
  ITEM_CLASS = 'IC',
  TAG = 'tg',
  ARCHETYPELIST = 'ARCH',
  TAB = 'tb',
  CUSTOM = 'CT',
  MICROLOOP = 'ML',
  LANGUAGE = 'LANG',
  WEAPON = 'WP',
  WEAPON_UPGRADE = 'WU',
  PASSIVE = 'P',
  PASSIVESkill = 'Pk',
  PARTICLE = 'PR',
  POWER_UP = 'PW',
  POWER_UP_STAT = 'PWS',
  MEMORY_MODULE = 'MM',
  EFFECT = 'EF',
  DROP = 'DP',
  CODE_BLOCK_DROP = 'CB',
  PARTICLE_DROP = 'PD',
  INGREDIENT_DROP = 'ID',
  INGREDIENT_VARIANCE = 'IV',
  PARTICLE_VARIANCE = 'PV',
  SPECIAL_WEAPON = 'SW',
  SPECIAL_WEAPONSHC = 'SWHC',
  UPGRADES = 'UP',
  LABORATORY = 'LB',
  TITANIUM_MINING = 'TM',
  HELLNIUM_MINING = 'HM',
  ADAMANTIUM_MINING = 'AM',
  TITANIUM_STORAGE = 'TS',
  ADAMANTIUM_STORAGE = 'AS',
  AI_PROMPT = 'AP',
  HELLNIUM_STORAGE = 'HS',
  BLUEPRINT_ARCHIVE = 'BA',
  SOULS_GRINDER = 'SG',
  PROFANARIUM = "PF",
  COLLECTIBLERARITYGOLD = "CRG",
  COLLECTIBLERARITYCODEBLOCKS = "CRCB",
  COLLECTIBLERARITYSOULS = "CRS",
  MASTERY = "MAS",
  TIER = "TR",
  STATUS = "ST",
  STATUS_EFFECT = "STE",
  STATUSINFO = 'STI',
  PRIMALMODIFIER = 'PM',
  LEVELPOINTS = 'LP',
  UNIQUECHARACTERES = 'UNC',
  TOTALARCHETYPES = 'TAR',
  UNIQUECARACTERSBYHCANDBL = 'UNCB',
  BATTLEUPGRADE = 'BU',
  BATTLEINFERIOR = 'BI',
  SILICATOS = 'SI',
  MAPS = 'MP',
  KEYWORDTAG = 'KWT',
  WEAPONRARITY = 'WR',
  COMMONWEAPONS = 'COW',
  CHARACTERSSELECTOR = 'CS',
  ITEMSSELECTOR = 'IS',
  ANIMATIONSSELECTOR = 'ANS',
  STORY_EXPANSION_PKG = 'SE',
  MODIFIER = 'MO',
  ATRIBUTTE = 'AT',
  MODMOONATTRIBUTES = 'MMATR',
  ELEMENTALDEFENSE = 'EL',
  CHEST = 'CH',
  KNOWLEDGE = 'KN',
  LEVELSPOKEPLACE = 'LSPOKEPLACE',
  MISSIONNOTES = 'MISNOTES',
}

export enum Name {
  
  AREA = 'Area',
  LEVEL = 'Level',
  DIALOGUE = 'Dialogue',
  DILEMMABOX = 'DilemmaBox',
  DILEMMA = 'Dilemma',  
  ANSWERDILEMMABOX = 'AnswerDilemmaBox',
  OPTION_BOX = 'OptionBox',
  STORY_BOX = 'StoryBox',
  SPEECH = 'Speech',
  MARKER = 'Marker',
  EVENT = 'Event',
  OPTION = 'Option',
  SCENERY = 'Scenery',
  CHARACTER = 'Character',
  CLASS = 'Class',
  EMOTION = 'Emotion',
  MISSION = 'Mission',
  OBJECTIVE = 'Objective',
  ITEM = 'Item',
  VIDEO = 'Video',
  THEME = 'Theme',
  TUTORIAL = 'Tutorial',
  MICROLOOP = 'Microloop',
  ROADBLOCK = 'RoadBlock'
}

export const NameToPrefix = {

  [Name.AREA]: IdPrefixes.AREA,
  [Name.LEVEL]: IdPrefixes.LEVEL,
  [Name.DIALOGUE]: IdPrefixes.DIALOGUE,
  [Name.DILEMMABOX]: IdPrefixes.DILEMMABOX,
  [Name.DILEMMA]: IdPrefixes.DILEMMA,  
  [Name.ANSWERDILEMMABOX]: IdPrefixes.ANSWERDILEMMABOX,
  [Name.OPTION_BOX]: IdPrefixes.OPTIONBOX,
  [Name.STORY_BOX]: IdPrefixes.STORYBOX,
  [Name.SPEECH]: IdPrefixes.SPEECH,
  [Name.MARKER]: IdPrefixes.MARKER,
  [Name.EVENT]: IdPrefixes.EVENT,
  [Name.OPTION]: IdPrefixes.OPTION,
  [Name.SCENERY]: IdPrefixes.SCENERY,
  [Name.CHARACTER]: IdPrefixes.CHARACTER,
  [Name.CLASS]: IdPrefixes.CLASS,
  [Name.EMOTION]: IdPrefixes.EMOTION,
  [Name.MISSION]: IdPrefixes.MISSION,
  [Name.OBJECTIVE]: IdPrefixes.OBJECTIVE,
  [Name.ITEM]: IdPrefixes.ITEM,
  [Name.VIDEO]: IdPrefixes.VIDEO,
  [Name.THEME]: IdPrefixes.ANIMATIC_THEME,
  [Name.TUTORIAL]: IdPrefixes.TUTORIAL,
  [Name.MICROLOOP]: IdPrefixes.MICROLOOP,
  [Name.ROADBLOCK]: IdPrefixes.ROADBLOCK
}
