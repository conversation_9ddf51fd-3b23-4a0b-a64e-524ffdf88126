export enum MarkerType 
{
  //The boss type is used to help differentiate the boss type from marker type
  BOSS_TYPE = 0,
  UNLOCK_LEVEL = 2,
  RELEASE_DIALOGUE = 3,
  BLOCK_BATTLE = 4,
  FINISH_DIALOGUE = 5,
  REMOVE_SPECIAL_ITEM = 6,
  REMOVE_SPECIAL_ITEM_ON_GRIND = 13,
  PIN = 7,
  MARK_COLLECTIBLE = 8,
  MICROLOOP = 9,
  RESTART_DIALOGUE = 10,
  BLOCK_MAP = 11,
  BLOCK_GRIND = 12,
  ATTACK_ADVANTAGE = 14,
  KNOWLEDGE_POINT = 15
}
