import { RPGType } from '../../rpg-formatting';
import { VideoType, EventType, MarkerType, DialogueType, OptionBoxType, ItemType, ItemProperty, Gender, LevelType, Voices, CharacterType } from '../enums';

export const rpgFormattingTypeName: { [enumValue: number]: string } = {
  [RPGType.CHARACTER]: 'Character',
  [RPGType.CURRENCY]: 'Currency',
  [RPGType.GENERAL_ITEM]: 'General Item',
  [RPGType.PLACE]: 'Place',
};

/**
 * display name for a VideoType as a string
 */
export const videoTypeName: { [enumValue: number]: string } = {
  [VideoType.CUTSCENE]: 'Cutscene',
  [VideoType.ANIMATIC]: 'Animatic',
};

/**
 * display name for an EventType as a string
 */
export const eventTypeName: { [enumValue: number]: string } = {
  [EventType.RECEIVE_ITEM]: 'Receive an Item',
  [EventType.GIVE_ITEM]: 'Give an Item',
  [EventType.TRADE_ITEM]: 'Trade an Item',
  [EventType.ASSIGN_MISSION]: 'Assign a Mission',
  [EventType.PLAY_VIDEO]: 'Play a Video',
  [EventType.PLAY_TUTORIAL]: 'Play a Tutorial',
  [EventType.FAIL_OBJECTIVE]: 'Fail Objective',
  [EventType.COMPLETE_OBJECTIVE]: 'Complete Objective',
  [EventType.REMOVE_SPECIAL_ITEM]: 'Remove Special Item',
  [EventType.REMOVE_SPECIAL_ITEM_ON_GRIND]: 'Remove Special Item On Grind',
  [EventType.REFUSE_PAYMENT]: 'Refuse payment',
  [EventType.PAY_BRIBE]: 'Pay Bribe',
  [EventType.EVADE]: 'Evade',
  [EventType.LINK_MICROLOOP]: 'Link to Microloop',
  [EventType.DIARY_EVENT]: 'Diary Event'
};

/**
 * display name for a MarkerType as a string
 */
export const markerTypeName: { [enumValue: number]: string } = {
  [MarkerType.UNLOCK_LEVEL]: 'Unlock Level',
  [MarkerType.RELEASE_DIALOGUE]: 'Unlock Release Dialog',
  [MarkerType.BLOCK_BATTLE]: 'Block Battle',
  [MarkerType.FINISH_DIALOGUE]: 'Finish Dialog',
  [MarkerType.REMOVE_SPECIAL_ITEM]: 'Remove Special Item',
  [MarkerType.REMOVE_SPECIAL_ITEM_ON_GRIND]: 'Remove Special Item On Grind',
  [MarkerType.PIN]: 'Pin',
  [MarkerType.MARK_COLLECTIBLE]: 'Mark as Collectible',
  [MarkerType.MICROLOOP]: 'Microloop',
  [MarkerType.RESTART_DIALOGUE]: 'Restart Dialog',
  [MarkerType.BLOCK_MAP]: 'Block Map',
  [MarkerType.BLOCK_GRIND]: 'Block Grind',
  [MarkerType.ATTACK_ADVANTAGE]: 'Attack Advantage',
  [MarkerType.KNOWLEDGE_POINT]: 'Knowledge Point'
};

/**
 * display name for a DialogueType as a string
 */
export const dialogueTypeName: { [enumValue: number]: string } = {
  [DialogueType.INIT]: 'Init',
  [DialogueType.END]: 'End',
  [DialogueType.RETURN]: 'Return',
  [DialogueType.RELEASE]: 'Release',
  [DialogueType.SPECIAL_RETURN]: 'Special Return',
};

export const dialogueTypeDisplay: { [enumValue: number]: string } = {
  [DialogueType.INIT]: 'Init',
  [DialogueType.END]: 'End',
  [DialogueType.RETURN]: 'Return B',
  [DialogueType.RELEASE]: 'Release Dialog',
  [DialogueType.SPECIAL_RETURN]: 'Return A',
};

/**
 * display name for an OptopnBoxType as a string
 */
export const optionBoxTypeName: { [enumValue: number]: string } = {
  [OptionBoxType.CHOICE]: 'Choice',
  [OptionBoxType.INVESTIGATION]: 'Investigation'
};

/**
 * display name for an ItemType as a string
 */
export const itemTypeName: { [enumValue: number]: string } = {
  [ItemType.CURRENCY]: 'Currency',
  [ItemType.POTION]: 'Potion',
  [ItemType.PROBLEM]: 'Problem',
  [ItemType.KEY]: 'Key',
};

/**
 * display name for an ItemType as a string
 */
export const itemPropertyName: { [enumValue: number]: string } = {
  [ItemProperty.DOSES]: 'Doses',
  [ItemProperty.ENERGY]: 'Energy',
  [ItemProperty.TIME]: 'Time',
};

/**
 * display name for a LevelType as a string
 */
export const levelTypeName: { [enumValue: number]: string } = {
  [LevelType.DEFAULT]: 'Default',
  [LevelType.MINION]: 'Minion',
  [LevelType.BOSS]: 'Boss',
  [LevelType.TRANSITION]: 'Transition',
  [LevelType.MINIGAME]: 'Minigame'
};

/**
 * display name for a Gender as a string
 */
export const genderName: { [enumValue: number]: string } = {
  [Gender.UNDEFINED]: 'undefined',
  [Gender.UNKNOWN]: 'Unknown',
  [Gender.FEMALE]: 'Female',
  [Gender.MALE]: 'Male',
};

/**
 * display name for a Gender as a string
 */
export const voiceName: { [enumValue: number]: string } = {
  [Voices.HUMAN_AND_NONHUMAN]: 'Human & Non-Human',
  [Voices.HUMAN]: 'Human',
  [Voices.NONHUMAN]: 'Non-Human',
};

/**
 * display name for a LevelType as a string
 */
export const levelTypeColor: { [enumValue: number]: string } = {
  [LevelType.BOSS]: '#a581e9',
  [LevelType.MINION]: '#E6E6E6',
  [LevelType.TRANSITION]: '#BADA55',
  [LevelType.DEFAULT]: '#00CCFF',
  [LevelType.MINIGAME]: '#ffa2fe'
};

/**
 * display name for a CharacterType as a string
 */
export const characterTypeName: { [enumValue: number]: string } = {
  [CharacterType.BOSS]: 'Boss',
  [CharacterType.MINION]: 'Minion',
  [CharacterType.SUBBOSS]: 'Subboss',
  [CharacterType.NPC]: 'NPC',
  [CharacterType.SECONDARY]: 'Secondary',
  [CharacterType.UNDEFINED]: 'undefined',
};

/**
 * html color for a VideoType as a string
 */
export const videoTypeColor: { [enumValue: number]: string } = {
  [VideoType.CUTSCENE]: '#e6a9d1',
  [VideoType.ANIMATIC]: '#6eaca9',
};

/**
 * html color for a CharacterType as a string
 */
export const characterTypeColor: { [enumValue: number]: string } = {
  [CharacterType.BOSS]: '#9062e4',
  [CharacterType.MINION]: '#E6E6E6',
  [CharacterType.SUBBOSS]: '#eb5f99',
  [CharacterType.NPC]: '#2bc08e',
  [CharacterType.SECONDARY]: '#e5e751',
  [CharacterType.UNDEFINED]: '#white',
};

export type language = string;