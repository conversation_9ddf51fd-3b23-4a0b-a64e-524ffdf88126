export interface Result {
  parametersWithErrors?: string[];
  dialogueId?: string;
  levelId?: string;
  assignedAt?: string[];
  assignedInDifferentDialogues?: boolean;
  assignedToBindArea?: boolean;
  isBattleCharacter?: boolean;
  type?: number;

  blankQnA?: boolean;

  objectivesCompleted?: boolean;
  hasObjectives?: boolean;
  receivedAt?: string[];
  givenAt?: string[];
  tradedAt?: string[];
  diaryAt?: string[];
  completesAt?: string[];
  failsAt?: string[];
  characterIds?: string[];

  asBattleCharacter?: string[];
  asBattleCharacterCount?: number;
  asBattleCharacterOnAssignedArea?: string[];
  asBattleCharacterOnAssignedAreaCount?: number;
  asSpeaker?: string[];

  match3BlockedBy?: string[];
  unlockedBy?: string[];
  unlockedMissionDialogueBy?: string[];
  linkListInOrder?: boolean;
  missionDialogueNotYetUnlocked?: boolean;

  parent_levelIds?: string[];
  parent_levelIds_otherArea?: string[];
  parent_levelIds_sameArea?: string[];

  child_levelIds?: string[];
  child_levelIds_otherArea?: string[];
  child_levelIds_sameArea?: string[];

  notTransition_linksToOtherArea?: boolean;
  isTransition_noLinksToOtherArea?: boolean;

  noBattleCharacters?: boolean;
  index?: number;
  amountOfWords?: number;

  hasTranslation?: boolean;
  hasFullHierarchicalTranslation?: boolean;
  redundantTranslation?: boolean;
  
}

