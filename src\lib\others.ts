import { SearchOptions } from 'src/app/components/pieces/header-search/header-search.component';
import { StringMatch } from './darkcloud';
//import { StringMatch } from 'src/lib/darkcloud';
import { FILTER_SUFFIX_PATH } from './darkcloud/angular/dsadmin/constants/others';

export function AmountOfWords(str: string): number {
  if(!str?.trim) return -1;
  return str?.trim().split(/\W+/).length || 0;
}

export function indexesOfSubsequence<T>(seq: T[], sub: T[]): number[] {
  const indexes: number[] = [];
  const startIndexes: number[] = [];
  seq.forEach((t, index) => {
    if (t === sub[0]) {
      startIndexes.push(index);
    }
  });
  startIndexes.forEach((startIndex) => {
    if (sub.every((v, i, a) => seq[startIndex + i] === v)) {
      indexes.push(startIndex);
    }
  });
  return indexes;
}

export function indexesOfMatch(str: string, regex: RegExp): number[] {
  const indexes: number[] = [];
  let match: RegExpExecArray = regex.exec(str);
  while (match) {
    indexes.push(match.index);
    match = regex.exec(str);
  }
  return indexes;
}

function getFilterValues(typeName: string) {
  return (
    JSON.parse(localStorage.getItem(`${typeName}.${FILTER_SUFFIX_PATH}`)) || {}
  );
}
export function setFilterValue(
  value: any,
  typeName: string,
  filterName: string
) {
  const filterValues = getFilterValues(typeName);
  filterValues[filterName] = value;
  localStorage.setItem(
    `${typeName}.${FILTER_SUFFIX_PATH}`,
    JSON.stringify(filterValues)
  );
}
export function getFilterValue(typeName: string, filterName: string): any {
  const filterValues = getFilterValues(typeName);
  return filterValues[filterName];
}

export function stringIsEqual(str: string, str2: string, options?: SearchOptions): boolean {
  return comparableString(str, options) === comparableString(str2, options);
}

export function comparableString(str: string, options?: SearchOptions) {

  if(!options)
  {
    return removeAccents(str)?.toLocaleUpperCase();
  }
  else
  {
    if(!options.accentSensitive)
      str = removeAccents(str);
    if(!options.caseSensitive)
      str = str?.toLocaleUpperCase();
    return str;
  }
}

export function removeAccents(str: string) {
  return str?.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
}

export interface Index<T> {
  [id: string]: T;
}

export interface ReviewRequest {
  typeName: string;
  fullReview?: boolean;
  parameters: {
  objectId?: string;
  levelId?: string;
  };
}

export function getInputList(
  list: any[],
  valueParameter: string,
  byParameters: string[],
  orderByParameter: boolean
): { [inputValue: string]: string } {
  const res = {};
  list = orderByParameter ? sortData(list, byParameters[0]) : list;
  list.forEach((element) => {
    res[element[valueParameter]] =
      (element[byParameters[0]] || '') +
      (byParameters[1] ? ': ' + element[byParameters[1]] : '');
  });
  return res;
}

export function getIconInputList(
  list: any[],
  orderByParameter: boolean
): { [inputValue: string]: string } {
  const res = {};
  list = orderByParameter
    ? list.sort((a, b) => {
        return a > b ? 1 : -1;
      })
    : list;
  list.forEach((element) => {
    res[element] = element.split('7s-')[1];
  });
  return res;
}

export function readMultiple(objs): string[] {
  const str: string[] = [];
  objs.forEach((obj) => {
    str.push(read(obj));
  });
  return str;
}

export function read(obj: any): string {
  // if (!obj) { return; }
  return obj.name ? obj.id + ': ' + obj.name : obj.id;
}

export function comparableNumber(str: any): number {
  if(!(str instanceof String))
  {
    if(str !== undefined)
    {
      str = str.toString();
    }
  }
  if (str === undefined || str?.replace(/[^0-9]/g, '') === '') {
    return undefined;
  }
  return parseFloat(str);
}

export function extractString(str: string): string {
  return str?.replace(/[0-9]/g, '');
}

export function extractSubId(str: string): string {
  const splittedId = str.split('.');
  let id = splittedId[splittedId.length - 1];
  const i = id.indexOf('_');
  id = i === -1 ? id : id.substring(0, i);
  return id;
}

export function extractPrefix(str: string): string {
  return extractString(extractSubId(str));
}

export function extractInt(str: string): number {
  return parseInt(str?.replace(/[^0-9]/g, ''), 10);
}

export function sortData<T, G extends T[] = T[]>(data: G, parameter: string): G 
{
  if (parameter === 'index') 
  {
    return data;
  } 
  else if (parameter === 'id') 
  {
    return data.sort((a, b) => 
    {
      return extractInt(a[parameter as string]) > extractInt(b[parameter as string]) ? 1 : -1;
    });
  } 
  else if (parameter === 'hierarchyCode') 
  {
    return data.sort((a, b) => compareCodes(a[parameter as string], b[parameter as string]) > 0 ? 1 : -1);
  } 
  else 
  {
    return data.sort((a, b) => 
    {
      return comparable(a[parameter]) > comparable(b[parameter]) || a[parameter] === undefined ? 1 : -1;
    });
  }
}

export function sortValues<T, V>(
  data: T[],
  getValue: (t: T) => V,
  order: 1 | -1
): T[] {
  const v = order * 1;
  if (data.length === 0) {
    return data;
  }

  const firstObj = data.find((t) => getValue(t) !== undefined);
  if (firstObj === undefined) {
    return data;
  }
  const firstValue: V = getValue(firstObj);
  if (typeof firstValue === 'number') {
    return data.sort((a, b) => {
      return +getValue(a) > +getValue(b)
        ? v * -1
        : +getValue(a) === +getValue(b)
        ? 0
        : v;
    });
    // return data.sort((a, b) => { return data.indexOf(a) > data.indexOf(b) ? 1 : -1; });
  } else if (typeof firstValue === 'string') {
    return data.sort((a, b) =>
      (getValue(a) as any as string) > (getValue(b) as any as string)
        ? v
        : (getValue(a) as any as string) === (getValue(b) as any as string)
        ? 0
        : v * -1
    );
  } else if (typeof firstValue === 'boolean') {
    return data.sort((a, b) => {
      return getValue(a) ? (!!getValue(a) === !!getValue(b) ? 0 : v * -1) : v;
    });
  } else {
    return data.sort((a, b) => {
      return comparable(getValue(a)) > comparable(getValue(b)) ||
        getValue(a) === undefined
        ? v
        : v * -1;
    });
  }
}

export function compareCodes(a: string, b: string): number {
  if (a === undefined) {
    return -1;
  }
  if (b === undefined) {
    return 1;
  }

  const aCode = a.match(StringMatch.ALPHA_MATCHER);
  const bCode = b.match(StringMatch.ALPHA_MATCHER);
  let weight = 0;

  aCode.forEach((aCodePart, i) => {
    if (weight !== 0) {
      return;
    }
    const bCodePart = bCode[i];
    if (bCodePart === undefined) {
      weight = 1;
      return;
    }
    const aString = extractString(aCodePart);
    if (aString) {
      const bString = extractString(bCodePart);
      weight = aString > bString ? 1 : aString === bString ? 0 : -1;
    } else {
      const aNumber = extractInt(aCodePart);
      const bNumber = extractInt(bCodePart);
      weight = aNumber - bNumber;
    }
  });

  return weight;
}

export function comparable(value: any, options?: SearchOptions) {
  if (typeof value === 'string') {
    return value ? comparableString(value, options) : undefined;
  }
  return value;
}

export function HighlightElement(
  elementId: string,
  miliseconds: number,
  scroll: boolean,
  color?: string,
  dontReset?: boolean,
  arg?: boolean | ScrollIntoViewOptions
) {
  if (miliseconds) {
   setTimeout(() => {
      Highlight(elementId, scroll, color, dontReset, arg);
    }, miliseconds);
  } else {
    Highlight(elementId, scroll, color, dontReset, arg);
  }
}


function Highlight(elementId: string, scroll: boolean, color?: string, dontReset?: boolean, arg?: boolean | ScrollIntoViewOptions)
{
  const element: HTMLElement = document.getElementById(elementId);
  if (element != null)
  {
    // preserve the element original background color
    const lastBackgroundColor = element.style.backgroundColor.toString();
    // highlight the element
    element.style.backgroundColor = color || 'rgb(186, 218, 85)';
    if (scroll)
    {
      // Use smooth scrolling with better options
      const scrollOptions: ScrollIntoViewOptions = {
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest'
      };

      // If custom options are provided, merge them with smooth behavior
      if (arg && typeof arg === 'object') {
        Object.assign(scrollOptions, arg, { behavior: 'smooth' });
      }

      element.scrollIntoView(scrollOptions);
    }
    if (!dontReset)
    {
     setTimeout(() =>
     {
        // reset the highlight of the element
        element.style.backgroundColor = lastBackgroundColor;
      }, 300);
    }
  }

}
